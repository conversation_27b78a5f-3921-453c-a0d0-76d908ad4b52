import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";

import { Position, Selection, TextDocument, TextEditor, Uri } from "../../__mocks__/vscode-mocks";
import { expandSelectionsToLineBoundaries, getSelectedCodeDetails } from "../../utils/editor";

describe("expandSelectionsToLineBoundaries", () => {
    let emptySelection = new Selection(0, 0, 0, 0);
    let whitespaceSelection = new Selection(1, 7, 1, 9);
    let multiLineSelection = new Selection(1, 3, 4, 2);

    let editor = new TextEditor(
        new TextDocument(
            Uri.parse("file://example/file"),
            "Line 1\nLine 2   \nLine 3\nLine 4\nLine 5"
        )
    );

    it("does not expand empty selection", () => {
        editor.selections = [emptySelection];
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].start).toEqual(new Position(0, 0));
        expect(editor.selections[0].end).toEqual(new Position(0, 0));
    });

    it("does not expand selection with only whitespace", () => {
        editor.selections = [whitespaceSelection];
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selection).toEqual(whitespaceSelection);
    });

    it("expands selection to line boundaries", () => {
        editor.selections = [multiLineSelection];
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].start).toEqual(new Position(1, 0));
        expect(editor.selections[0].end).toEqual(new Position(4, 6));
    });

    it("keeps cursor at start of selection when selection.active isBefore selection.anchor", () => {
        editor.selections = [multiLineSelection];
        expect(editor.selections[0].anchor).toEqual(new Position(1, 3));
        expect(editor.selections[0].active).toEqual(new Position(4, 2));
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].anchor).toEqual(new Position(1, 0));
        expect(editor.selections[0].active).toEqual(new Position(4, 6));
    });

    it("keeps cursor at end when selection.active isAfter selection.anchor", () => {
        editor.selections = [new Selection(multiLineSelection.active, multiLineSelection.anchor)];
        expect(editor.selections[0].anchor).toEqual(new Position(4, 2));
        expect(editor.selections[0].active).toEqual(new Position(1, 3));
        expandSelectionsToLineBoundaries(editor);
        expect(editor.selections[0].anchor).toEqual(new Position(4, 6));
        expect(editor.selections[0].active).toEqual(new Position(1, 0));
    });
});

describe("getSelectedCodeDetails", () => {
    // Mock WorkspaceManager
    const mockWorkspaceManager = {
        safeResolvePathName: jest.fn((_uri) => {
            return new QualifiedPathName("/repo/root", "test.ts");
        }),
    } as any;

    const suggestedPrefixCharCount = 1000;
    const suggestedSuffixCharCount = 1000;

    describe("newline handling", () => {
        it("should move only the first newline when suffix is all whitespace", () => {
            const document = new TextDocument(Uri.parse("file://test.ts"), "line1\nline2\n   \n\n");
            const editor = new TextEditor(document);

            // Select from start to end of line 2, leaving only whitespace in suffix
            editor.selection = new Selection(0, 0, 1, 5);

            const result = getSelectedCodeDetails(
                editor,
                mockWorkspaceManager,
                suggestedPrefixCharCount,
                suggestedSuffixCharCount
            );

            expect(result).not.toBeNull();
            expect(result!.selectedCode).toBe("line1\nline2\n");
            expect(result!.suffix).toBe("   \n\n");
        });

        it("should not move newline when suffix contains non-whitespace content", () => {
            const document = new TextDocument(Uri.parse("file://test.ts"), "line1\nline2\nline3\n");
            const editor = new TextEditor(document);

            // Select from start to end of line 1, but suffix contains non-whitespace content
            editor.selection = new Selection(0, 0, 0, 5);

            const result = getSelectedCodeDetails(
                editor,
                mockWorkspaceManager,
                suggestedPrefixCharCount,
                suggestedSuffixCharCount
            );

            expect(result).not.toBeNull();
            expect(result!.selectedCode).toBe("line1");
            expect(result!.suffix).toBe("\nline2\nline3\n");
        });

        it("should not move newline when selection ends at last line", () => {
            const document = new TextDocument(Uri.parse("file://test.ts"), "line1\nline2\nline3");
            const editor = new TextEditor(document);

            // Select from start to end of last line
            editor.selection = new Selection(0, 0, 2, 5);

            const result = getSelectedCodeDetails(
                editor,
                mockWorkspaceManager,
                suggestedPrefixCharCount,
                suggestedSuffixCharCount
            );

            expect(result).not.toBeNull();
            expect(result!.selectedCode).toBe("line1\nline2\nline3");
            expect(result!.suffix).toBe("");
        });

        it("should handle multiple whitespace lines correctly - only move the first newline", () => {
            const document = new TextDocument(Uri.parse("file://test.ts"), "line1\n   \n\t\n  \n");
            const editor = new TextEditor(document);

            // Select from start to end of line 1, suffix contains only whitespace
            editor.selection = new Selection(0, 0, 0, 5);

            const result = getSelectedCodeDetails(
                editor,
                mockWorkspaceManager,
                suggestedPrefixCharCount,
                suggestedSuffixCharCount
            );

            expect(result).not.toBeNull();
            expect(result!.selectedCode).toBe("line1\n");
            expect(result!.suffix).toBe("   \n\t\n  \n");
        });
    });
});
