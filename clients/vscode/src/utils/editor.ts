import * as vscode from "vscode";

import { getLogger } from "../logging";
import { FileDetails } from "../webview-providers/webview-messages";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { expandToLineBoundaries } from "./position";
import { Diagnostic, DiagnosticSeverity, SelectedCodeDetails } from "./types";

export function expandSelectionsToLineBoundaries(editor: vscode.TextEditor | undefined): boolean {
    if (!editor) {
        return false;
    } // Not handled
    editor.selections = editor.selections.map((selection) => {
        const selectedCode = editor.document.getText(selection);

        // Don't perform expansion if the selection is empty/all whitespace
        if (selectedCode.trim() === "") {
            return selection;
        }

        const newSelection = expandToLineBoundaries(selection, editor.document);

        // Keep the cursor at the start or the end depending on where it started
        if (selection.active.isBefore(selection.anchor)) {
            return new vscode.Selection(newSelection.end, newSelection.start);
        } else {
            return new vscode.Selection(newSelection.start, newSelection.end);
        }
    });
    return true;
}

/**
 * Retrieves diagnostic messages for the current selection in the active editor.
 *
 * This function fetches VSCode diagnostics (errors, warnings, etc.) that intersect
 * with the current selection in the active editor. It then extracts the messages from these diagnostics.
 *
 * @returns An array of diagnostic message strings. Returns an empty array if there's no active editor,
 *          no selection, or if the selection is empty.
 *
 * @throws Logs an error if an error occurs while filtering diagnostics or fetching diagnostics.
 */
export function getDiagnostics(): Diagnostic[] {
    const editor: vscode.TextEditor | undefined = vscode.window.activeTextEditor;
    if (!editor) {
        return [];
    }
    const { document, selection } = editor;
    let diagnostics: Diagnostic[] = [];
    if (!selection.isEmpty) {
        const allDiagnostics = vscode.languages.getDiagnostics(document.uri);
        const vscodeDiagnostics = allDiagnostics.filter((diagnostic) =>
            diagnostic.range.intersection(selection)
        );
        diagnostics = vscodeDiagnostics.map((diagnostic) => {
            return {
                location: {
                    path: document.fileName,
                    /* eslint-disable @typescript-eslint/naming-convention */
                    line_start: diagnostic.range.start.line,
                    line_end: diagnostic.range.end.line,
                },
                char_start: 0,
                char_end: 0,
                blob_name: "",
                current_blob_name: "",
                /* eslint-enable @typescript-eslint/naming-convention */
                message: diagnostic.message,
                severity: ((): DiagnosticSeverity => {
                    switch (diagnostic.severity) {
                        case vscode.DiagnosticSeverity.Error:
                            return DiagnosticSeverity.error;
                        case vscode.DiagnosticSeverity.Warning:
                            return DiagnosticSeverity.warning;
                        case vscode.DiagnosticSeverity.Information:
                            return DiagnosticSeverity.information;
                        case vscode.DiagnosticSeverity.Hint:
                            return DiagnosticSeverity.hint;
                        default:
                            return DiagnosticSeverity.error; // Default to Error if unknown
                    }
                })(),
            };
        });
    }
    return diagnostics;
}

/**
 * Returns the selected code, prefix, suffix, path, and language of the current editor.
 *
 * Note that this function *does not perform expansion* of the selection.
 *
 * @param editor A vscode text editor
 * @returns SelectedCodeDetails | null
 */
export function getSelectedCodeDetails(
    editor: vscode.TextEditor | undefined,
    workspaceManager: WorkspaceManager,
    suggestedPrefixCharCount: number,
    suggestedSuffixCharCount: number
): SelectedCodeDetails | null {
    if (!editor) {
        // Return null if there's no active editor
        return null;
    }
    const { document, selection } = editor;
    const qualifiedPath = workspaceManager.safeResolvePathName(document.uri);
    if (!qualifiedPath) {
        // This should not happen -- files does not exist?
        getLogger("getSelectedCodeDetails").error("Unable to resolve path name for document");
        return null;
    }

    const language = document.languageId;

    // Get the start and end of the document
    const startOfFile = document.lineAt(0).range.start;
    const endOfFile = document.lineAt(document.lineCount - 1).range.end;

    let selectedCode = document.getText(selection);

    let prefixRange: vscode.Range;
    let suffixRange: vscode.Range;

    // If the exact selected text is empty (only whitespace), treat as if no text was selected
    if (selectedCode.trim() === "") {
        // Prefix is everything before and including the cursor's current line
        // Go until start of line after current active cursor line
        const cursorPos = new vscode.Position(selection.active.line + 1, 0);

        prefixRange = new vscode.Range(startOfFile, cursorPos);
        suffixRange = new vscode.Range(cursorPos, endOfFile);
    } else {
        prefixRange = new vscode.Range(startOfFile, selection.start);
        suffixRange = new vscode.Range(selection.end, endOfFile);
    }
    var prefix = document.getText(prefixRange);
    var suffix = document.getText(suffixRange);

    if (prefix.length > suggestedPrefixCharCount) {
        prefix = prefix.slice(prefix.length - suggestedPrefixCharCount);
    }
    if (suffix.length > suggestedSuffixCharCount) {
        suffix = suffix.slice(0, suggestedSuffixCharCount);
    }
    // AU-5989 - VSCode leaves the last newline outside the selected code, we want it inside and not in the suffix
    if (suffix.trim() === "" && suffix.length > 0) {
        // Find the first newline character(s) in the suffix
        if (suffix.startsWith("\r\n")) {
            selectedCode += "\r\n";
            suffix = suffix.slice(2);
        } else if (suffix.startsWith("\n")) {
            selectedCode += "\n";
            suffix = suffix.slice(1);
        }
    }
    return {
        selectedCode,
        prefix: prefix,
        suffix: suffix,
        path: qualifiedPath.relPath,
        language,
        prefixBegin: prefixRange.start.character,
        suffixEnd: suffixRange.end.character,
    };
}

export async function replaceFileWithCodeblock(
    editor: vscode.TextEditor,
    code: string
): Promise<void> {
    if (!editor) {
        return;
    }

    const document = editor.document;
    const range = new vscode.Range(
        document.lineAt(0).range.start,
        document.lineAt(document.lineCount - 1).range.end
    );

    await editor.edit((editBuilder) => {
        editBuilder.replace(range, code);
    });
}

export function replaceSelectionWithCodeblock(
    editor: vscode.TextEditor,
    code: string,
    selectReplacement: boolean = false
): void {
    if (!editor?.selection?.active) {
        return;
    }

    const cursorSelection = editor.selection;

    // We need to construct the snippet string and then `appendText` in
    // order to escape strings properly and insert it as plaintext. For the
    // snippet docs describing this, see the below link:
    // https://macromates.com/textmate/manual/snippets#plain-text
    // The VSCode docs describing this are here:
    // https://code.visualstudio.com/api/references/vscode-api#SnippetString
    const snippet = new vscode.SnippetString().appendText(code);

    // Snippets take advantage of language features, formatting, etc.
    // For more details: https://code.visualstudio.com/docs/editor/userdefinedsnippets
    void editor.insertSnippet(snippet, cursorSelection);

    if (selectReplacement) {
        const lines = code.split("\n");
        const numLines = lines.length;

        const newSelection = new vscode.Selection(
            cursorSelection.start,
            cursorSelection.start.translate(numLines - 1, 0)
        );

        editor.selection = newSelection;
    }
}

export function compareFileDetails(a: FileDetails, b: FileDetails): number {
    if (a.repoRoot !== b.repoRoot) {
        return a.repoRoot.localeCompare(b.repoRoot);
    }
    return a.pathName.localeCompare(b.pathName);
}
