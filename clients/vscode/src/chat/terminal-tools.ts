import { Exchange, TerminalInfo } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { getAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";
import { AgentSessionEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
import { CommandTimeoutPredictor } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/command-timeout-predictor";
import {
    checkShellAllowlist,
    getShellAllowlist,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-allowlist";
import { ShellProcessTools } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-process-tools";
import {
    getDefaultShell,
    isSupportedShell,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/shell-utils";
import {
    errorToolResponse,
    successToolResponse,
} from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/tool-use-response";
import {
    ToolBase,
    ToolSafety,
    ToolUseResponse,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ShellConfig, TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { executeCommand } from "@augment-internal/sidecar-libs/src/tools/tool-utils";
import { delayMs } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { withTimeout } from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { truncateMiddle } from "@augment-internal/sidecar-libs/src/utils/strings";
import {
    TruncateOptions,
    truncateWithMetadata,
} from "@augment-internal/sidecar-libs/src/utils/truncation-utils";
import {
    TruncatedContentType,
    UntruncatedContentManager,
} from "@augment-internal/sidecar-libs/src/utils/untruncated-content-manager";
import { spawn, spawnSync } from "child_process";
import * as crypto from "crypto";
import * as fs from "fs";
import * as os from "os";
import path from "path";
import { split } from "shlex";
import * as vscode from "vscode";

import { AugmentLogger, getLogger } from "../logging";
import { AugmentGlobalState, FileStorageKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { isVsCodeVersionGte } from "../utils/environment";
import {
    createTempDirectory,
    deleteDirectory,
    directoryExists,
    fileOrSymlinkToFileExists,
    readDirectorySync,
    writeFileUtf8,
} from "../utils/fs-utils";
import { isAbsolutePathName } from "../utils/path-utils";
import { FileType } from "../utils/types";
import { LocalToolType } from "../webview-providers/tool-types";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { getCwdForTool } from "./tools-utils";

/**
 * Information about script and shell processes
 */
interface ScriptProcessInfo {
    scriptPid: number;
    shellPid: number;
}

/**
 * Strategies for detecting when terminal commands complete
 */
enum CompletionDetectionStrategy {
    /** Use VSCode's built-in terminal events (unreliable) */
    vscodeEvents = "vscode_events",
    /** Use script/transcript to capture output and detect completion */
    scriptCapture = "script_capture",
}

/**
 * Process execution status
 */
enum ProcessStatus {
    /** Process is still running */
    running = "running",
    /** Process completed normally */
    completed = "completed",
    /** Process was killed/terminated */
    killed = "killed",
}

/**
 * Converts a string strategy name to the corresponding enum value
 */
function parseCompletionDetectionStrategy(
    strategy: string,
    logger: AugmentLogger
): CompletionDetectionStrategy {
    switch (strategy) {
        case "vscode_events":
            return CompletionDetectionStrategy.vscodeEvents;
        case "script_capture":
            return CompletionDetectionStrategy.scriptCapture;
        default:
            logger.warn(`Unknown strategy from feature flag: ${strategy}, using auto-selection`);
            return CompletionDetectionStrategy.vscodeEvents;
    }
}

/**
 * Creates a temporary directory with an empty .zshrc file for zsh
 * This is a shared utility function that can be used by both TerminalProcessTools and ScriptCaptureStrategy
 * @returns Path to the temporary directory
 */
async function createZshTempEnvironment(): Promise<string> {
    const tempDir = await createTempDirectory("augment-zsh-");
    await writeFileUtf8(path.join(tempDir, ".zshrc"), "# Empty .zshrc file created by Augment\n");
    return tempDir;
}

/**
 * Common interface for completion detection strategies
 */
interface ICompletionDetectionStrategy {
    /** Set up the strategy for a terminal (e.g., create files, define functions) */
    setupTerminal(
        terminal: vscode.Terminal,
        shellName: string,
        startupScript: string
    ): Promise<boolean>;

    /** Modify a command to include completion detection */
    wrapCommand(
        command: string,
        processId: number,
        terminal: vscode.Terminal,
        trackCommandCompletion: boolean
    ): string;

    /** Check if a process has completed */
    checkCompleted(processId: number, terminal: vscode.Terminal): CompletionResult;

    /** Get output and return code from completed process (for strategies that capture output) */
    getOutputAndReturnCode?(
        processId: number,
        terminal: vscode.Terminal,
        actualCommand: string,
        isCompleted?: boolean
    ): { output: string; returnCode: number | null } | undefined;

    /** Clean up resources for a terminal */
    cleanupTerminal(terminal: vscode.Terminal): void;

    /** Check if terminal session is still active and reinitialize if needed */
    ensureTerminalSessionActive?(
        terminal: vscode.Terminal,
        fallbackShellName?: string
    ): Promise<boolean>;

    /**
     * Executes a command in the terminal and waits for it to complete.
     * @returns true if the command completed, false if it timed out
     */
    runCommandUntilCompletion(
        terminal: vscode.Terminal,
        command: string,
        commandName: string,
        timeoutEventName?: AgentSessionEventName,
        timeoutMs?: number
    ): Promise<boolean>;
}

/**
 * Result of completion detection, distinguishing between command completion and shell connection
 */
interface CompletionResult {
    isCompleted: boolean;
}

/**_getLastCommand
 * VSCode events strategy - relies on built-in terminal events
 */
class VSCodeEventsStrategy implements ICompletionDetectionStrategy {
    private _logger = getLogger("VSCodeEventsStrategy");

    constructor(private readonly _terminalProcessTools?: TerminalProcessTools) {}
    async setupTerminal(
        terminal: vscode.Terminal,
        _shellName: string,
        startupScript: string
    ): Promise<boolean> {
        // Run startup script if available
        if (startupScript) {
            terminal.sendText(startupScript, true);

            // Wait a bit to let the script execute before continuing.
            // Unfortunately, it's difficult to know if the startup script has completed.
            // Executing it with executeCommand/sendText means we can get an arbitrary number of
            // execution complete messages, each of which can be one or more lines.
            // We could instead write it to a temp file and execute that or add a special marker,
            // but we'd then expose that to the user and we'd have to figure out how to handle
            // timeouts.  So we just wait a bit and hope it's done.
            await delayMs(100);
        }
        return true;
    }

    wrapCommand(
        command: string,
        _processId: number,
        _terminal: vscode.Terminal,
        _trackCommandCompletion: boolean
    ): string {
        return command;
    }

    //TODO(shawn): move the check completion logic used for VSCode event strategy here
    checkCompleted(_processId: number, _terminal: vscode.Terminal): CompletionResult {
        return { isCompleted: false };
    }

    cleanupTerminal(_terminal: vscode.Terminal): void {}

    async runCommandUntilCompletion(
        terminal: vscode.Terminal,
        command: string,
        commandName: string,
        timeoutEventName: AgentSessionEventName,
        timeoutMs: number = 1000
    ): Promise<boolean> {
        let lastCommand = "";

        terminal.sendText(command);
        /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */
        // Block execution until the above command completes.
        // Without this we sometimes fail to read the output of the command....
        let pollerInterval: NodeJS.Timeout | null = null;
        // Flag to ensure only one instance of the async function runs at a time.
        let isExecuting = false;
        await withTimeout(
            new Promise((resolve) => {
                pollerInterval = setInterval(() => {
                    // Only start a new execution if not already executing
                    if (!isExecuting) {
                        isExecuting = true;
                        void (async () => {
                            try {
                                this._logger.verbose(`Polling for ${commandName} command result`);
                                lastCommand =
                                    (await this._terminalProcessTools?.getLastCommand(terminal))
                                        ?.lastCommand || "";
                                if (lastCommand === command) {
                                    if (pollerInterval) {
                                        clearInterval(pollerInterval);
                                        pollerInterval = null;
                                    }
                                    this._logger.debug(
                                        `Successfully got ${commandName} command result.`
                                    );
                                    resolve(void 0);
                                }
                            } finally {
                                // Always reset the flag when done
                                isExecuting = false;
                            }
                        })();
                    }
                }, 100);
            }),
            timeoutMs
        )
            .catch(() => {
                this._logger.debug(`Timed out waiting for ${commandName} command to complete`);
                getAgentSessionEventReporter().reportEvent({
                    eventName: timeoutEventName,
                    conversationId: "",
                });
                return false;
            })
            .finally(() => {
                if (pollerInterval) {
                    clearInterval(pollerInterval);
                    pollerInterval = null;
                }
            });
        return lastCommand === command;
    }
}

interface TerminalSessionData {
    scriptFile?: string;
    shellName?: string;
    scriptPid?: number;
    shellPid?: number;
    lastChildProcesses?: Set<number>; // Track child processes for process-based detection
    processCheckInProgress?: boolean; // Flag to avoid redundant process checks between background
    cwdTrackingFile?: string; // File path for tracking CWD changes via shell hooks
}

/**
 * Utility functions for tracking CWD changes using shell hooks
 */
class CwdTracker {
    /**
     * Generate shell-specific CWD tracking setup commands
     * @param shellName The shell name (zsh, bash, fish, etc.)
     * @param cwdTrackingFile Path to the file where CWD changes will be written
     * @returns Shell commands to set up CWD tracking
     */
    static generateCwdTrackingSetup(shellName: string, cwdTrackingFile: string): string {
        const escapedPath = _escapePathForShell(cwdTrackingFile, shellName);

        switch (shellName) {
            //Set up CWD tracking for zsh and initialize with current directory
            case "zsh":
                return `
chpwd() {
    echo "$(pwd)" > ${escapedPath}
}
echo "$(pwd)" > ${escapedPath}
`.trim();

            // Set up CWD tracking for bash and initialize with current directory.
            // Preserve existing PROMPT_COMMAND
            case "bash":
                return `
export PROMPT_COMMAND="\${PROMPT_COMMAND:+$PROMPT_COMMAND;}echo \\"\\$(pwd)\\" > ${escapedPath}"
echo "$(pwd)" > ${escapedPath}
`.trim();

            // Set up CWD tracking for fish and initialize with current directory
            case "fish":
                return `
function augment_track_cwd --on-variable PWD
    echo $PWD > ${escapedPath}
end
echo (pwd) > ${escapedPath}
`.trim();

            default:
                // For other shells, don't try to set up CWD tracking
                return "";
        }
    }

    /**
     * Read the current working directory from the tracking file
     * @param cwdTrackingFile Path to the CWD tracking file
     * @returns The current working directory or undefined if not available
     */
    static readCurrentCwd(cwdTrackingFile: string): string | undefined {
        try {
            if (!fs.existsSync(cwdTrackingFile)) {
                return undefined;
            }
            const content = fs.readFileSync(cwdTrackingFile, "utf8").trim();
            return content || undefined;
        } catch (error) {
            return undefined;
        }
    }

    /**
     * Clean up the CWD tracking file
     * @param cwdTrackingFile Path to the CWD tracking file
     */
    static cleanupCwdTracking(cwdTrackingFile: string): void {
        try {
            if (fs.existsSync(cwdTrackingFile)) {
                fs.unlinkSync(cwdTrackingFile);
            }
        } catch (error) {
            // Ignore cleanup errors
            getLogger("CwdTracker").debug(`Error cleaning up CWD tracking file: ${String(error)}`);
        }
    }
}

/**
 * Hybrid script-marker strategy - starts script once, uses markers for completion detection
 */
class ScriptCaptureStrategy implements ICompletionDetectionStrategy {
    private _terminalSessions = new Map<vscode.Terminal, TerminalSessionData>();
    private _logger = getLogger("ScriptCaptureStrategy");

    async setupTerminal(
        terminal: vscode.Terminal,
        shellName: string,
        startupScript: string
    ): Promise<boolean> {
        this._logger.info(`setUpTerminal for ${shellName} in script capture strategy`);

        const tempDir = os.tmpdir();
        const randomId = crypto.randomBytes(8).toString("hex");

        // Set up script capture file
        const scriptFile = path.join(tempDir, `augment-script-${randomId}.log`);
        // Set up CWD tracking file
        const cwdTrackingFile = path.join(tempDir, `augment-cwd-${randomId}.txt`);

        // Start script capture first
        const scriptCommand = await this._generateScriptStartCommand(shellName, scriptFile);

        terminal.sendText(scriptCommand, true);
        const scriptProcessInfo = await this._waitForScriptReady(scriptCommand, scriptFile);
        let scriptPid: number | undefined;
        let shellPid: number | undefined;

        if (!scriptProcessInfo) {
            this._logger.error(`Failed to start script capture for terminal ${terminal.name}`);
        } else {
            scriptPid = scriptProcessInfo.scriptPid;
            shellPid = scriptProcessInfo.shellPid;

            this._logger.debug(`run script capture command completed: ${scriptCommand}`);

            // Initialize session data for this terminal
            this._terminalSessions.set(terminal, {
                scriptFile,
                shellName,
                scriptPid,
                shellPid,
                lastChildProcesses: new Set(),
                cwdTrackingFile,
            });
        }

        if (startupScript) {
            const result = await this.runCommandUntilCompletion(
                terminal,
                startupScript,
                "startup script",
                AgentSessionEventName.vsCodeTerminalTimedOutWaitingForStartupCommand
            );
            if (!result) {
                this._logger.error(`Failed to run startup script for terminal ${terminal.name}`);
            }
        }

        // Set up CWD tracking using shell hooks
        const cwdSetupCommand = CwdTracker.generateCwdTrackingSetup(shellName, cwdTrackingFile);
        if (cwdSetupCommand) {
            const cwdResult = await this.runCommandUntilCompletion(
                terminal,
                cwdSetupCommand,
                "CWD tracking setup"
            );
            if (!cwdResult) {
                this._logger.warn(`Failed to set up CWD tracking for terminal ${terminal.name}`);
            } else {
                this._logger.debug(
                    `CWD tracking set up successfully for terminal ${terminal.name}. tracking file: ${cwdTrackingFile}`
                );
            }
        }

        // Clear the terminal to hide the setup commands from the user
        const clearCommand = _getClearCommand(shellName);
        await this.runCommandUntilCompletion(terminal, clearCommand, "clear command");

        return scriptPid !== undefined && shellPid !== undefined;
    }

    private async _waitForScriptReady(
        scriptCommand: string,
        scriptFile: string
    ): Promise<ScriptProcessInfo | null> {
        const maxAttempts = 10; // Wait up to 2 seconds
        const interval = 200;

        this._logger.debug(`Waiting for script to be ready: ${scriptCommand}`);

        for (let i = 0; i < maxAttempts; i++) {
            // First check if we can find the script process PID
            const scriptProcessInfo = this._findScriptProcess(scriptCommand, scriptFile);

            if (scriptProcessInfo && fs.existsSync(scriptFile)) {
                return scriptProcessInfo;
            }

            await delayMs(interval);
        }

        this._logger.debug(`Script not ready after ${maxAttempts * interval}ms`);
        return null;
    }

    wrapCommand(
        command: string,
        processId: number,
        terminal: vscode.Terminal,
        trackCommandCompletion: boolean
    ): string {
        // Clear the script file before running the new command to ensure we only read output for this command
        const sessionData = this._terminalSessions.get(terminal);

        if (!sessionData) {
            this._logger.error(`No session data found for terminal in wrapCommand`);
            return command;
        }

        this._terminalSessions.set(terminal, sessionData);
        if (sessionData.scriptFile) {
            try {
                // Clear the script file to ensure we only capture output from the current command
                fs.writeFileSync(sessionData.scriptFile, "", "utf8");
                this._logger.debug(
                    `Cleared script file ${sessionData.scriptFile} before running command for process ${processId}`
                );
            } catch (error) {
                this._logger.debug(
                    `Error clearing script file ${sessionData.scriptFile} before running command for process ${processId}: ${String(error)}`
                );
            }
        }

        // Store current child processes before command execution for comparison
        if (sessionData.shellPid && trackCommandCompletion) {
            void this._captureChildProcessesSnapshot(sessionData.shellPid, sessionData);
        }
        this._terminalSessions.set(terminal, sessionData);
        return command;
    }

    checkCompleted(processId: number, terminal: vscode.Terminal): CompletionResult {
        this._logger.debug(
            `Checking for completion of process: ${processId} using process-based detection`
        );
        const sessionData = this._terminalSessions.get(terminal);
        if (!sessionData) {
            this._logger.error(`No session data found for terminal in checkCompleted`);
            return { isCompleted: false };
        }
        return this._checkCompletedProcessBased(processId, sessionData);
    }

    /**
     * Check if a process check is currently in progress for the given terminal
     * Used by background polling to avoid race conditions
     */
    isProcessCheckInProgress(terminal: vscode.Terminal): boolean {
        const sessionData = this._terminalSessions.get(terminal);
        return sessionData?.processCheckInProgress === true;
    }

    /**
     * Process-based completion detection for regular commands
     */
    private _checkCompletedProcessBased(
        processId: number,
        sessionData: TerminalSessionData | undefined
    ): CompletionResult {
        let isCompleted: boolean = false;

        // Process-based detection for regular commands
        if (sessionData?.shellPid) {
            try {
                // Set flag to indicate process check is in progress
                sessionData.processCheckInProgress = true;
                isCompleted = this._checkProcessBasedCompletion(
                    sessionData.shellPid,
                    sessionData,
                    processId
                );
            } catch (error) {
                this._logger.debug(`Error in process check: ${String(error)}`);
            } finally {
                // Always clear the flag when done
                sessionData.processCheckInProgress = false;
            }
        } else {
            this._logger.debug(
                `No shell PID found for terminal, cannot use process-based detection`
            );
        }

        return { isCompleted };
    }

    async ensureTerminalSessionActive(
        terminal: vscode.Terminal,
        fallbackShellName?: string
    ): Promise<boolean> {
        if (!this.isReady(terminal)) {
            this._logger.debug(`Script session is dead for terminal, reinitializing`);

            const sessionData = this._terminalSessions.get(terminal);
            let shellName = sessionData?.shellName;

            // If no shell name in session data, fall back to provided shell name
            if (!shellName && fallbackShellName) {
                this._logger.debug(
                    `No shell name in session data, using fallback: ${fallbackShellName}`
                );
                shellName = fallbackShellName;
            }

            if (!shellName) {
                this._logger.error("No shell name found for terminal and no fallback provided");
                return false;
            }

            // First try lightweight formatting reset
            // TODO (shawn): figure out the command to run on windows
            terminal.sendText("reset\n");
            await delayMs(300);

            return await this.setupTerminal(terminal, shellName, "");
        } else {
            return true;
        }
    }

    getScriptPid(terminal: vscode.Terminal): number | undefined {
        return this._terminalSessions.get(terminal)?.scriptPid;
    }

    getOutputAndReturnCode(
        processId: number,
        terminal: vscode.Terminal,
        actualCommand: string,
        isCompleted?: boolean
    ): { output: string; returnCode: number | null } | undefined {
        const sessionData = this._terminalSessions.get(terminal); // sessionData should always exist after setupTerminal

        if (!sessionData) {
            return undefined;
        }

        const scriptFile = sessionData.scriptFile;
        if (!scriptFile) {
            return undefined;
        }

        try {
            if (!fs.existsSync(scriptFile)) {
                return undefined;
            }

            // Since we clear the script file before each command in wrapCommand(), we can read the entire content
            const rawContent = fs.readFileSync(scriptFile, "utf8");

            // Parse the script output to extract only the command results
            const parsedOutput = this._parseScriptOutput(rawContent, actualCommand, isCompleted);

            this._logger.debug(
                `Read ${rawContent.length} characters from script file for process ${processId}, parsed to ${parsedOutput.length} characters`
            );
            return { output: parsedOutput, returnCode: null };
        } catch {
            return undefined;
        }
    }

    /**
     * Parse script command output to extract only the actual command results
     */
    private _parseScriptOutput(
        rawContent: string,
        actualCommand: string,
        isCompleted?: boolean
    ): string {
        // Find the command in the raw content first (before stripping ANSI codes)
        // because stripping ANSI codes would have removed the command.
        // we are using the command matching to find the start of the output and
        // get rid of the noisy interactive editing commands captured on the screen
        const commandIndex = rawContent.indexOf(actualCommand);
        if (commandIndex >= 0) {
            // Remove everything up to and including the first occurrence of the command
            rawContent = rawContent.substring(commandIndex + actualCommand.length);
            this._logger.debug(`Filtered out command using lastIndexOf: "${actualCommand}"`);
        }

        let cleanedText = _stripControlCodes(rawContent);

        // Only remove the last line if the command has completed
        if (isCompleted) {
            cleanedText = cleanedText.trim().split("\n").slice(0, -1).join("\n");
        }

        return cleanedText.trim();
    }

    cleanupTerminal(terminal: vscode.Terminal): void {
        this._logger.debug(`Cleaning up terminal: ${terminal.name}`);
        const sessionData = this._terminalSessions.get(terminal);
        if (!sessionData) {
            return;
        }

        // Kill script process first
        if (sessionData.scriptPid) {
            try {
                this._killScriptProcess(sessionData.scriptPid);
                this._logger.debug(
                    `Initiated cleanup of script process for PID ${sessionData.scriptPid}`
                );
            } catch (error) {
                this._logger.debug(
                    `Error killing script process for PID ${sessionData.scriptPid}: ${String(error)}`
                );
            }
        }

        // Clean up script file
        if (sessionData.scriptFile) {
            try {
                if (fs.existsSync(sessionData.scriptFile)) {
                    fs.unlinkSync(sessionData.scriptFile);
                }
            } catch (error) {
                // Ignore cleanup errors - temp files will be cleaned up by OS eventually
            }
        }

        // Clean up CWD tracking file
        if (sessionData.cwdTrackingFile) {
            CwdTracker.cleanupCwdTracking(sessionData.cwdTrackingFile);
        }

        // Clean up session data
        this._terminalSessions.delete(terminal);
    }

    /**
     * Get the current working directory from CWD tracking file
     * @param terminal The terminal to get CWD for
     * @returns The current working directory or undefined if not available
     */
    getCurrentCwd(terminal: vscode.Terminal): string | undefined {
        const sessionData = this._terminalSessions.get(terminal);
        if (!sessionData?.cwdTrackingFile) {
            return undefined;
        }
        return CwdTracker.readCurrentCwd(sessionData.cwdTrackingFile);
    }

    /**
     * Run a command within the script session and wait for completion
     */
    async runCommandUntilCompletion(
        terminal: vscode.Terminal,
        command: string,
        _commandName: string,
        timeoutEventName?: AgentSessionEventName,
        timeoutMs: number = 3000
    ): Promise<boolean> {
        const processId = Date.now();
        const wrappedCommand = this.wrapCommand(command, processId, terminal, true);
        terminal.sendText(wrappedCommand, false);
        terminal.sendText("");

        const startTime = Date.now();

        return new Promise((resolve) => {
            const pollInterval = setInterval(() => {
                try {
                    const result = this.checkCompleted(processId, terminal);
                    if (result.isCompleted) {
                        clearInterval(pollInterval);
                        resolve(true);
                        return;
                    }

                    if (Date.now() - startTime > timeoutMs) {
                        this._logger.warn(`Command timed out: ${command}`);
                        clearInterval(pollInterval);
                        if (timeoutEventName) {
                            getAgentSessionEventReporter().reportEvent({
                                eventName: timeoutEventName,
                                conversationId: "",
                            });
                        }
                        resolve(false);
                    }
                } catch (error) {
                    // Continue polling on errors
                    this._logger.debug(`Error in polling: ${String(error)}`);
                }
            }, 200);
        });
    }

    /**
     * Find child processes of a given parent PID using spawnSync for better performance
     * @param parentPid - The parent process ID
     * @returns Array of child process IDs
     */
    private _getChildProcesses(parentPid: number): number[] {
        try {
            this._logger.debug(
                `Getting child processes for PID ${parentPid} using: pgrep -P ${parentPid}`
            );

            const platform = process.platform;

            if (platform !== "linux" && platform !== "darwin") {
                this._logger.debug(`Unsupported platform: ${platform}`);
                return [];
            }

            const result = spawnSync("pgrep", ["-P", parentPid.toString()], {
                encoding: "utf8",
                timeout: 5000,
            });

            // pgrep returns exit code 1 when no processes are found, which is normal
            if (result.status !== 0) {
                this._logger.debug(
                    `No child processes found for PID ${parentPid}. Error: ${result.error?.message}. Output: ${result.stdout}`
                );
                return [];
            }

            const childPids: number[] = [];
            const lines = result.stdout.split("\n");

            // Each line is a PID
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (trimmedLine) {
                    const pid = parseInt(trimmedLine);
                    if (!isNaN(pid) && pid > 0) {
                        childPids.push(pid);
                    } else {
                        this._logger.debug(`Invalid PID in line: "${trimmedLine}"`);
                    }
                }
            }
            return childPids;
        } catch (error) {
            this._logger.debug(
                `Error getting child processes for PID ${parentPid}: ${String(error)}`
            );
            return [];
        }
    }

    /**
     * Check if a process is running using spawnSync (synchronous)
     * @param pid - The process ID to check
     * @returns boolean - true if process is running
     */
    private _isInRunningState(pid: number): boolean {
        try {
            const platform = process.platform;

            let psArgs: string[];
            if (platform === "linux" || platform === "darwin") {
                psArgs = ["-p", pid.toString(), "-o", "state"];
            } else {
                this._logger.debug(`Unsupported platform: ${platform}`);
                return false;
            }

            this._logger.debug(
                `Checking if process ${pid} is running using: ps ${psArgs.join(" ")}`
            );

            const result = spawnSync("ps", psArgs, {
                encoding: "utf8",
            });

            if (result.status !== 0) {
                // Process not found
                this._logger.debug(
                    `Process ${pid} is not running (ps command failed): Error: ${result.error?.message}. Output: ${result.stdout}`
                );
                return false;
            }

            const lines = result.stdout.trim().split("\n");
            // Skip header line  (first line contains column headers)
            const dataLines = lines.slice(1);
            const state = dataLines.length > 0 ? dataLines[0].trim() : "";

            // Process is only considered running if it's in "R" (Running) state
            // Other states like "S" (Sleep), "D" (Uninterruptible sleep), "Z" (Zombie), etc. are not considered running
            const isRunning = state === "R";
            this._logger.debug(`Process ${pid} is running: ${isRunning} (state: "${state}")`);
            return isRunning;
        } catch (error) {
            this._logger.debug(
                `Error checking process: ${error instanceof Error ? error.message : String(error)}`
            );
            return false;
        }
    }

    /**
     * Capture a snapshot of current child processes for later comparison
     * @param shellPid - The shell process ID
     * @param sessionData - The terminal session data to update
     */
    private _captureChildProcessesSnapshot(
        shellPid: number,
        sessionData: TerminalSessionData
    ): void {
        try {
            const childPids = this._getChildProcesses(shellPid);
            sessionData.lastChildProcesses = new Set(childPids);
            this._logger.debug(
                `Captured child processes snapshot for shell PID ${shellPid}: ${Array.from(sessionData.lastChildProcesses).join(", ")}`
            );
        } catch (error) {
            this._logger.debug(`Error capturing child processes snapshot: ${String(error)}`);
            sessionData.lastChildProcesses = new Set();
        }
    }

    /**
     * Check if a command has completed using process-based detection
     * @param shellPid - The shell process ID
     * @param sessionData - The terminal session data
     * @param processId - The process ID for logging
     * @returns boolean - true if command has completed
     */
    private _checkProcessBasedCompletion(
        shellPid: number,
        sessionData: TerminalSessionData,
        processId: number
    ): boolean {
        try {
            // Get current child processes
            const currentChildPids = this._getChildProcesses(shellPid);

            // Compare with previous snapshot
            const previousChildSet = sessionData.lastChildProcesses || new Set();
            const hasNewChildren = currentChildPids.some((pid) => !previousChildSet.has(pid));

            this._logger.debug(
                `Previous child PIDs: [${Array.from(previousChildSet).join(", ")}], ` +
                    `Current child PIDs: [${currentChildPids.join(", ")}]`
            );

            if (hasNewChildren) {
                this._logger.debug(`Process-based completion not detected for ${processId}`);
                return false;
            }

            // Check if shell process is in running state
            const shellRunning = this._isInRunningState(shellPid);
            this._logger.debug(`shell running=${shellRunning}`);

            if (shellRunning) {
                this._logger.debug(`Process-based completion not detected for ${processId}`);
                return false;
            }

            // Command is considered completed when:
            // 1. Shell itself is not in running state &&
            // 2. No new child processes
            this._logger.debug(`Process-based completion confirmed for ${processId}`);
            return true;
        } catch (error) {
            this._logger.debug(`Error in process-based completion check: ${String(error)}`);
            return false;
        }
    }

    /**
     * Find the script process and its shell child process by matching the script file path
     * @param _scriptCommand - The script command pattern (for logging)
     * @param scriptFile - The script file path to match
     * @returns ScriptProcessInfo with scriptPid and optional shellPid, or null if not found
     */
    private _findScriptProcess(
        _scriptCommand: string,
        scriptFile: string
    ): ScriptProcessInfo | null {
        try {
            // Use different ps command options based on platform
            const platform = process.platform;
            let psArgs: string[];

            if (platform === "linux") {
                // Linux: use -eo format
                psArgs = ["-eo", "pid,ppid,comm,args"];
            } else if (platform === "darwin") {
                // macOS: use -A -o format
                psArgs = ["-A", "-o", "pid,ppid,comm,args"];
            } else {
                this._logger.debug(`Unsupported platform: ${platform}`);
                return null;
            }

            const result = spawnSync("ps", psArgs, {
                encoding: "utf8",
            });

            if (result.status !== 0) {
                this._logger.debug(
                    `ps command failed in _findScriptProcess. Error: ${result.error?.message}. Output: ${result.stdout}`
                );
                return null;
            }

            const lines = result.stdout.trim().split("\n");
            // Skip header line  (first line contains column headers)
            const dataLines = lines.slice(1);

            let scriptPid: number | null = null;
            let shellPid: number | null = null;

            // First pass: find the script process
            for (const line of dataLines) {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 4) {
                    const pid = parseInt(parts[0]);
                    const comm = parts[2];
                    const args = parts.slice(3).join(" "); // Full command line

                    // Look for script command that contains our script file path
                    if (comm === "script" && args.includes(scriptFile)) {
                        this._logger.debug(`Found script process ${pid} by file path: ${args}`);
                        scriptPid = pid;
                        break;
                    }
                }
            }

            if (!scriptPid) {
                this._logger.debug(`No script process found matching file path: ${scriptFile}`);
                return null;
            }

            // Second pass: find the shell process (child of script)
            for (const line of dataLines) {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 4) {
                    const pid = parseInt(parts[0]);
                    const ppid = parseInt(parts[1]);

                    if (ppid === scriptPid) {
                        shellPid = pid;
                        this._logger.debug(
                            `Found shell process ${pid} for script PID ${scriptPid}`
                        );
                        break;
                    }
                }
            }

            if (!shellPid) {
                this._logger.debug(`No shell process found for script PID ${scriptPid}`);
                return null;
            }

            return {
                scriptPid,
                shellPid: shellPid,
            };
        } catch (error) {
            this._logger.debug(`Error finding script process: ${String(error)}`);
            return null;
        }
    }

    /**
     * Kill script process
     * Called during terminal cleanup when the terminal is already closing
     * The script process should handle cleanup of its child processes automatically
     */
    private _killScriptProcess(pid: number): void {
        try {
            // Just kill the script process - it should handle its children
            process.kill(pid, "SIGTERM");
            this._logger.debug(`Sent SIGTERM to script process ${pid}`);

            setTimeout(() => {
                try {
                    process.kill(pid, "SIGKILL");
                    this._logger.debug(`Force killed script process ${pid}`);
                } catch {
                    // Process already dead
                }
            }, 1000);
        } catch {
            // Process may already be dead
            this._logger.debug(`Script process ${pid} may already be dead`);
        }
    }

    private async _generateScriptStartCommand(
        shellName: string,
        scriptFile: string
    ): Promise<string> {
        const escapedPath = _escapePathForShell(scriptFile, shellName);
        const isLinux = process.platform === "linux";

        // Startpl script capture in the background
        switch (shellName) {
            case "powershell":
                return `Start-Transcript -Path ${escapedPath} -Append`;
            case "zsh": {
                // For zsh, ensure .zshrc exists to prevent configuration wizard
                const zdotdir = await this._ensureZshConfigExists();
                const zdotdirPrefix = zdotdir
                    ? `ZDOTDIR=${_escapePathForShell(zdotdir, shellName)} `
                    : "";
                if (isLinux) {
                    return `${zdotdirPrefix}script -q -e -f ${escapedPath}\n`;
                } else {
                    return `${zdotdirPrefix}script -q -e -F ${escapedPath}\n`;
                }
            }
            case "bash":
            default:
                if (isLinux) {
                    return `script -q -e -f ${escapedPath}\n`;
                } else {
                    return `script -q -e -F ${escapedPath}\n`;
                }
        }
    }

    /**
     * Ensure zsh config file exists to prevent configuration wizard
     * Creates a minimal .zshrc file if it doesn't exist in home directory
     * @returns The directory path containing .zshrc (for use with ZDOTDIR)
     */
    private async _ensureZshConfigExists(): Promise<string | undefined> {
        try {
            const homeDir = os.homedir();
            const zshrcPath = path.join(homeDir, ".zshrc");

            // Check if .zshrc already exists in home directory
            try {
                await fs.promises.access(zshrcPath);
                this._logger.debug(`Found existing .zshrc file at ${zshrcPath}`);
                return homeDir; // Return home directory as ZDOTDIR
            } catch {
                const tempZshDir = await createZshTempEnvironment();
                this._logger.debug(
                    `Created minimal .zshrc file at ${path.join(tempZshDir, ".zshrc")}`
                );
                return tempZshDir; // Return temp directory as ZDOTDIR
            }
        } catch (error) {
            this._logger.debug(`Error ensuring zsh config exists: ${String(error)}`);
            // Don't throw - this is not critical, just a convenience
            return undefined;
        }
    }

    /**
     * Check if the script session is still active for a terminal
     */
    public isReady(terminal: vscode.Terminal): boolean {
        this._logger.debug(
            `Checking script session for terminal: ${terminal.name}, total sessions: ${this._terminalSessions.size}`
        );
        const sessionData = this._terminalSessions.get(terminal);
        const scriptPid = sessionData?.scriptPid;
        if (!scriptPid) {
            this._logger.debug(
                `No script PID found for terminal ${terminal.name}, sessionData:${JSON.stringify(sessionData)}`
            );
            return false;
        }

        try {
            // Check if the script process is still running
            // process.kill(pid, 0) throws if process doesn't exist, returns true if it exists
            process.kill(scriptPid, 0);
            return true;
        } catch {
            // Process doesn't exist anymore
            this._logger.debug(`Script session PID ${scriptPid} is no longer active`);
            return false;
        }
    }
}

/**
 * Background polling manager for continuous process state monitoring
 *
 * This is only applicable for ScriptCaptureStrategy
 *
 * Command that are timed out in waitForProcess is stuck in running state
 * This class make sure to update those process state if the command completed itself,
 *  or killed by user manually.
 */
class ProcessPollingManager {
    private _pollingInterval: NodeJS.Timeout | undefined;
    private _isPolling: boolean = false;
    private _logger = getLogger("ProcessPollingManager");
    private readonly _pollIntervalMs = 10000; // 10 seconds

    constructor(private readonly _terminalProcessTools: TerminalProcessTools) {}

    startPolling(): void {
        if (this._isPolling) {
            return;
        }

        this._logger.info("Starting background process polling");
        this._isPolling = true;

        this._pollingInterval = setInterval(() => {
            this._pollActiveProcesses();
        }, this._pollIntervalMs);
    }

    stopPolling(): void {
        if (!this._isPolling) {
            return;
        }

        this._logger.info("Stopping background process polling");
        this._isPolling = false;

        if (this._pollingInterval) {
            clearInterval(this._pollingInterval);
            this._pollingInterval = undefined;
        }
    }

    private _pollActiveProcesses(): void {
        if (!this._terminalProcessTools) {
            return;
        }

        try {
            this._terminalProcessTools.updateProcessStatesFromPolling();
        } catch (error) {
            this._logger.error(`Error during background polling: ${String(error)}`);
        }
    }

    dispose(): void {
        this.stopPolling();
    }
}

/**
 * A helper class to manage processes and their output, which is used to implement
 * various process tools.
 */
interface TerminalProcess {
    terminal: vscode.Terminal;
    command: string;
    actualCommand: string;
    lastCommand: string;
    output: string;
    state: ProcessStatus;
    execution?: any;
    readStream?: AsyncIterable<string>;
    exitCode: number | null;
    toolUseId: string; // The actual tool use ID from the chat system
    seenStartEvent: boolean;
}

/**
 * Extended shell information with additional properties needed by terminal tools
 */
type ShellInfo = ShellConfig & {
    /** Path to a temporary directory that should be cleaned up when done */
    tempDir?: string;
};

function areSameShellInfo(a: ShellInfo, b: ShellInfo): boolean {
    return (
        a.name === b.name &&
        a.path === b.path &&
        a.args?.join(" ") === b.args?.join(" ") &&
        Object.keys(a.env ?? {}).join(" ") === Object.keys(b.env ?? {}).join(" ") &&
        Object.values(a.env ?? {}).join(" ") === Object.values(b.env ?? {}).join(" ")
    );
}

export class TerminalProcessTools extends DisposableService {
    private static _shellInfo: ShellInfo = TerminalProcessTools._getDefaultShell(); // Initialize with default, will be updated in _initializeShells
    private readonly _processes = new Map<number, TerminalProcess>();
    private readonly _waitResolvers = new Map<
        number,
        (result: { output: string; returnCode: number | null }) => void
    >();
    private _nextId = 1;
    private _activePoller?: NodeJS.Timeout;
    private static _longRunningTerminal?: vscode.Terminal;
    private _terminalNeedsUpdate: boolean = false;
    private _logger = getLogger("TerminalProcessTools");
    private readonly _maxOutputLength = 63 * 1024; // 64KiB - 1KiB buffer for additional text from the tools themselves
    // Completion detection strategy - static to persist across instances
    private static _completionStrategy: ICompletionDetectionStrategy | undefined;
    // Store all shells that pass capability checks
    private _supportedShells: Array<{
        shellInfo: ShellInfo;
        capability: TerminalCapabilityResult;
    }> = [];
    private _terminalSettings: TerminalSettings = { supportedShells: [] };
    private readonly _untruncatedContentManager?: UntruncatedContentManager;
    // Shell process tools for handling "exec" type shells
    private _shellProcessTools?: ShellProcessTools;
    private _isDisposed: boolean = false;
    // Background polling manager
    private _pollingManager?: ProcessPollingManager;
    // Smart timeout predictor
    private readonly _timeoutPredictor: CommandTimeoutPredictor;

    // Ensure we only show a warning once (per VSCode session) if shell integration is not enabled.
    // Otherwise we might show it on every mode switch, which could be annoying.
    private static _showedShellIntegrationMessage: boolean = false;

    constructor(
        private readonly _extensionRoot: vscode.Uri,
        private readonly _globalState: AugmentGlobalState,
        private readonly _enableStart: number,
        private readonly _enableUntruncatedContentStorage: boolean,
        private readonly _maxLinesTerminalProcessOutput: number,
        private readonly _truncationFooterAdditionText: string,
        strategy: string,
        private readonly _assetManager?: IPluginFileStore
    ) {
        super();

        // Initialize completion detection strategy - reuse static instance if available
        if (!TerminalProcessTools._completionStrategy) {
            this._logger.debug(`Creating new static completion strategy: ${strategy}`);
            TerminalProcessTools._completionStrategy = this._createCompletionStrategy(strategy);
        } else {
            this._logger.debug(`Reusing existing static completion strategy`);
        }

        if (TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy) {
            // Initialize background polling manager
            this._pollingManager = new ProcessPollingManager(this);
        }

        const shellIntegrationEnabled = vscode.workspace
            .getConfiguration("terminal.integrated.shellIntegration")
            .get("enabled");
        if (!shellIntegrationEnabled && !TerminalProcessTools._showedShellIntegrationMessage) {
            void vscode.window
                .showWarningMessage(
                    "Please enable 'Terminal > Integrated > Shell Integration: Enabled' in your VSCode settings for the Augment agent to be able to use terminals.",
                    "Open Settings"
                )
                .then((selection) => {
                    if (selection === "Open Settings") {
                        void vscode.commands.executeCommand(
                            "workbench.action.openSettings",
                            "@id:terminal.integrated.shellIntegration.enabled"
                        );
                    }
                });
            TerminalProcessTools._showedShellIntegrationMessage = true;
        }

        this._untruncatedContentManager = this._assetManager
            ? new UntruncatedContentManager(this._assetManager)
            : undefined;

        // Initialize smart timeout predictor
        this._timeoutPredictor = new CommandTimeoutPredictor(this._assetManager ?? null);

        // Register for notifications when terminal settings change
        this.addDisposable(
            this._globalState.onDidChangeFileStorage<TerminalSettings>(
                FileStorageKey.terminalSettings,
                (event) => {
                    this._terminalSettings = event.value;
                    this._logger.verbose("Terminal settings changed");
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalSettingsChanged,
                        conversationId: "",
                    });
                    this._checkAndUpdateShell();
                }
            )
        );

        // Load initial settings
        void this._globalState
            .load<TerminalSettings>(FileStorageKey.terminalSettings)
            .then((settings) => {
                this._terminalSettings = settings ?? { supportedShells: [] };
                this._logger.verbose("Initial terminal settings loaded");
                this._checkAndUpdateShell();
            })
            .catch((error) => {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                this._logger.debug(`Error loading initial terminal settings: ${error.message}`);
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalErrorLoadingSettings,
                    conversationId: "",
                });
                this._terminalSettings = { supportedShells: [] };
            });

        // Find supported shells and set the default shell
        void this._initializeShells();

        // Try to use shell integration if available.
        // These APIs are only available in VSCode 1.93.0 and above.
        // TODO: Call them directly once we build against that.
        /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */
        const window = vscode.window as any;
        if (window.onDidEndTerminalShellExecution && isVsCodeVersionGte("1.93.0")) {
            this._logger.verbose(`Registering for onDidEndTerminalShellExecution.`);
            this.addDisposable(
                window.onDidEndTerminalShellExecution(async (e: any) => {
                    this._logger.verbose(
                        `Got onDidEndTerminalShellExecution event: ${e.execution?.commandLine?.value}, ${e.exitCode}, ${e.execution?.commandLine?.confidence}, ${e.terminal?.name}`
                    );
                    // Find the terminal process.
                    // We want the last process with this terminal since many things can use the
                    // long-running terminal.
                    for (const [id, process] of Array.from(this._processes).reverse()) {
                        this._logger.verbose(
                            `Checking process ${id} for completion with ${e.execution === process.execution}.`
                        );
                        // Note that we do not check if the command that just ended is the exact same
                        // as the one we're expecting.  This is because VSCode sometimes corrupts that
                        // string.  While this might over-trigger, it hopefully won't, and we really
                        // don't want to miss this event and keep polling until the timeout.
                        if (
                            process.terminal === e.terminal &&
                            process.state !== ProcessStatus.killed &&
                            process.exitCode === null &&
                            (!process.execution || e.execution === process.execution)
                        ) {
                            if (!process.seenStartEvent) {
                                this._logger.verbose(
                                    `Process ${id} completed before we saw start event.`
                                );
                                getAgentSessionEventReporter().reportEvent({
                                    eventName: AgentSessionEventName.vsCodeTerminalMissedStartEvent,
                                    conversationId: "",
                                });
                                break;
                            }
                            // Update exitCode and get output
                            this._logger.verbose(`Process ${id} is complete.`);
                            process.exitCode = e.exitCode ?? null;
                            if (e.execution.commandLine.value !== process.command) {
                                this._logger.debug(
                                    `Command line for process ${id} does not match. Expected ${process.command} but got ${e.execution.commandLine.value}`
                                );
                                getAgentSessionEventReporter().reportEvent({
                                    eventName:
                                        AgentSessionEventName.vsCodeTerminalBuggyExecutionEvents,
                                    conversationId: "",
                                });
                            }
                            // In some cases where the string doesn't match exactly but is slightly
                            // wrong (e.g., due to physical wrapping), readStream seems to be correct.
                            // In other cases (e.g., zsh with p10k), it's empty and readStream is
                            // worse than using the clipboard.  We thus hope this generalizes.
                            // I love VSCode.
                            if (process.readStream && e.execution.commandLine.value.length > 0) {
                                // Wait a little bit to make sure the output is fully written.
                                // This also appears to fix an issue where `cwd` isn't
                                // updated on local machines right after a command is run
                                // in the terminal.
                                // NOTE(arun): This delay is a magic number: 1ms didn't work
                                // and a binary search indicated that 2ms seemed to work?
                                // Waiting for 10ms out of an abundance of caution.
                                await delayMs(10);
                                this._logger.debug(`Reading exact output for process ${id}`);
                                // Even if the process is complete, readStream calls might still hang,
                                // so we use a timeout here.
                                const rawOutput = await this._readProcessStreamWithTimeout(
                                    process.readStream,
                                    id
                                );
                                if (rawOutput === undefined) {
                                    // Timed out without reading any data, fall back to clipboard
                                    this._logger.debug(
                                        `Process ${id} timed out without reading data, falling back to clipboard`
                                    );
                                    getAgentSessionEventReporter().reportEvent({
                                        eventName:
                                            AgentSessionEventName.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete,
                                        conversationId: "",
                                    });
                                    process.output = await this._getOutputFromClipboard(id);
                                } else if (this._isBuggyOutput(rawOutput)) {
                                    this._logger.debug(
                                        `Buggy output detected for process ${id}. Please upgrade VSCode.`
                                    );
                                    getAgentSessionEventReporter().reportEvent({
                                        eventName: AgentSessionEventName.vsCodeTerminalBuggyOutput,
                                        conversationId: "",
                                    });
                                    process.output = await this._getOutputFromClipboard(id);
                                } else {
                                    // Strip control codes from the output
                                    const cleanOutput = _stripControlCodes(rawOutput);

                                    // Use our helper method to truncate the output
                                    process.output = await this._truncateOutput(
                                        cleanOutput,
                                        process.toolUseId,
                                        process.command
                                    );
                                }
                            } else {
                                process.output = await this._getOutputFromClipboard(id);
                            }

                            // For non-long-running terminals, dispose them.
                            // For long-running terminals, we just mark the process as complete.
                            if (process.terminal !== TerminalProcessTools._longRunningTerminal) {
                                process.terminal.dispose();
                            }
                            process.state = ProcessStatus.killed;

                            // Resolve any waiting promises
                            const resolver = this._waitResolvers.get(id);
                            if (resolver) {
                                resolver({
                                    output: process.output,
                                    returnCode: process.exitCode,
                                });
                            }
                            break;
                        }
                    }
                })
            );
        } else {
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalShellIntegrationNotAvailable,
                conversationId: "",
            });
        }

        if (window.onDidStartTerminalShellExecution && isVsCodeVersionGte("1.93.0")) {
            this.addDisposable(
                window.onDidStartTerminalShellExecution((e: any) => {
                    this._logger.verbose(
                        `Got onDidStartTerminalShellExecution event: ${e.execution?.commandLine?.value}, ${e.execution?.commandLine?.confidence}, ${e.terminal?.name}`
                    );
                    for (const [id, process] of Array.from(this._processes).reverse()) {
                        this._logger.verbose(`Checking process ${id} for starting.`);
                        if (
                            process.terminal === e.terminal &&
                            process.state !== ProcessStatus.killed &&
                            process.exitCode === null
                        ) {
                            this._logger.verbose(`Process ${id} is starting.`);
                            process.seenStartEvent = true;
                            break;
                        }
                    }
                })
            );
        }
        /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

        // Listen for the terminal getting closed.
        this.addDisposable(
            vscode.window.onDidCloseTerminal(async (terminal) => {
                this._logger.debug(`Got onDidCloseTerminal event: ${terminal.name}`);

                TerminalProcessTools._completionStrategy?.cleanupTerminal(terminal);

                // If the long-running terminal is closed, reset the static strategy
                if (terminal === TerminalProcessTools._longRunningTerminal) {
                    TerminalProcessTools._longRunningTerminal = undefined;
                }

                for (const [id, process] of this._processes) {
                    if (process.terminal === terminal && process.state !== ProcessStatus.killed) {
                        if (process.readStream) {
                            // If we do a for await we will hang forever.
                            process.output = await this._getOutputFromPossiblyIncompleteProcess(
                                process,
                                id
                            );
                        }

                        process.state = ProcessStatus.killed;
                        // Get the actual error code of the process.
                        process.exitCode = terminal.exitStatus?.code ?? -1;

                        const resolver = this._waitResolvers.get(id);
                        if (resolver) {
                            resolver({
                                output: process.output,
                                returnCode: process.exitCode,
                            });
                        }
                        break;
                    }
                }
            })
        );
    }

    /**
     * Creates the appropriate completion detection strategy based on configuration and shell capabilities
     */
    private _createCompletionStrategy(strategy: string): ICompletionDetectionStrategy {
        // Convert string strategy to enum value
        const selectedStrategy = parseCompletionDetectionStrategy(strategy, this._logger);
        switch (selectedStrategy) {
            case CompletionDetectionStrategy.vscodeEvents:
                this._logger.debug("Using VSCode events completion detection strategy");
                return new VSCodeEventsStrategy(this);
            case CompletionDetectionStrategy.scriptCapture:
                // Check if script command is available before using script capture strategy
                if (this._canUseScriptStrategy()) {
                    this._logger.debug("Using script capture completion detection strategy");
                    return new ScriptCaptureStrategy();
                } else {
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalScriptCommandNotAvailable,
                        conversationId: "",
                    });
                    this._logger.warn(
                        "Script command not available, falling back to VSCode events"
                    );
                    return new VSCodeEventsStrategy(this);
                }
            default:
                this._logger.warn(
                    `Unknown completion detection strategy: ${String(selectedStrategy)}, falling back to VSCode events`
                );
                return new VSCodeEventsStrategy(this);
        }
    }

    /**
     * Checks if the current shell is an "exec" type shell
     */
    private _isExecShell(): boolean {
        return TerminalProcessTools._shellInfo.friendlyName === "exec";
    }

    /**
     * Checks if the ScriptCaptureStrategy can be used.
     * This checks if the 'script' command is available on the system
     * or if the current shell is PowerShell.
     * @returns true if the script command exists, false otherwise
     */
    private _canUseScriptStrategy(): boolean {
        try {
            // TODO (shawn): once we implement and test ScriptCapture strategy on powershell, update this.
            // For now, we don't support it.
            if (this.shellName === "powershell") {
                return false;
            }

            // For Unix-like systems, check if 'script' command exists
            const result = spawnSync("script", ["--version"], {
                shell: false,
                stdio: "ignore",
                timeout: 1000,
            });

            // If the command executed without error, the script command exists
            return result.error === undefined;
        } catch (e) {
            // Any exception means the script command couldn't be checked
            this._logger.debug(`Error checking script command availability: ${String(e)}`);
            return false;
        }
    }

    /**
     * Checks if there's a saved shell preference and updates the current shell if needed
     * @returns True if the shell was updated, false otherwise
     */
    private _checkAndUpdateShell(): boolean {
        try {
            const savedShellName = this._terminalSettings.selectedShell;

            // If we have a saved shell preference and it's different from the current one
            if (savedShellName && savedShellName !== TerminalProcessTools._shellInfo.friendlyName) {
                // Find the shell in our supported shells
                const savedShell = this._supportedShells.find(
                    (s) => s.shellInfo.friendlyName === savedShellName
                );
                if (savedShell) {
                    // Update the current shell if it's running.
                    TerminalProcessTools._shellInfo = savedShell.shellInfo;
                    if (TerminalProcessTools._longRunningTerminal) {
                        this._terminalNeedsUpdate = true;
                    }
                    if (this._isExecShell()) {
                        this._shellProcessTools?.cleanup();
                        this._shellProcessTools = new ShellProcessTools(
                            TerminalProcessTools._shellInfo
                        );
                    }
                    this._logger.debug(
                        `Updated shell to saved preference: ${TerminalProcessTools._shellInfo.friendlyName}`
                    );
                    return true;
                }
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error checking for shell updates: ${error.message}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingForShellUpdates,
                conversationId: "",
            });
        }

        return false;
    }

    public async launch(
        command: string,
        cwd: string | undefined,
        abortSignal: AbortSignal,
        useLongRunningTerminal: boolean,
        toolUseId: string
    ): Promise<number | string> {
        // Check if given cwd is absolute path
        if (cwd && !isAbsolutePathName(cwd)) {
            const message = `Cannot launch process because the specified working directory is not an absolute path: ${cwd}`;
            this._logger.warn(message);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalCwdNotAbsolute,
                conversationId: "",
            });
            return message;
        }

        // Check if given cwd exists
        if (cwd && !directoryExists(cwd)) {
            const message = `Cannot launch process because the specified working directory does not exist: ${cwd}`;
            this._logger.warn(message);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalCwdDoesNotExist,
                conversationId: "",
            });
            return message;
        }

        // Check if we need to update the shell based on saved preferences
        this._checkAndUpdateShell();

        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.launch(command, cwd, abortSignal);
        }

        const id = this._nextId++;
        let isNewTerminal = false;
        let terminal: vscode.Terminal;
        if (useLongRunningTerminal) {
            // If the long-running terminal is gone, create a new one.
            if (
                !TerminalProcessTools._longRunningTerminal ||
                TerminalProcessTools._longRunningTerminal.exitStatus
            ) {
                TerminalProcessTools._longRunningTerminal = await this._createTerminal(
                    "Augment",
                    cwd
                );
                isNewTerminal = true;
            } else if (TerminalProcessTools._longRunningTerminal && this._terminalNeedsUpdate) {
                // If the terminal needs an update, dispose it and create a new one.
                TerminalProcessTools._longRunningTerminal.dispose();
                TerminalProcessTools._longRunningTerminal = await this._createTerminal(
                    "Augment",
                    cwd
                );
                this._terminalNeedsUpdate = false;
                isNewTerminal = true;
            } else if (
                // If there is an existing long-running terminal that doesn't have shell
                // integration, dispose it and create a new one.  We can't reuse the same terminal
                // because without shell integration we wouldn't know its ending cwd, which would
                // confuse the model.  But we want to leave the terminal open after the process
                // completes and until the next one starts so the user can see the output.
                TerminalProcessTools._longRunningTerminal &&
                !this._terminalHasShellIntegration(TerminalProcessTools._longRunningTerminal)
            ) {
                TerminalProcessTools._longRunningTerminal.dispose();
                TerminalProcessTools._longRunningTerminal = await this._createTerminal(
                    "Augment",
                    cwd
                );
                isNewTerminal = true;
            } else {
                this._logger.debug("Reusing existing long-running terminal");
            }
            terminal = TerminalProcessTools._longRunningTerminal;
        } else {
            terminal = await this._createTerminal("Augment", cwd);
        }

        // If we're trying to use the long-running terminal but it's in use, fail.
        if (useLongRunningTerminal) {
            const runningProcess = Array.from(this._processes.entries()).find(
                ([, process]) =>
                    process.terminal === terminal &&
                    process.exitCode === null &&
                    process.state === ProcessStatus.running
            );
            if (runningProcess) {
                const runningId = runningProcess[0];
                const output = await this.readOutput(runningId);

                return `\
Cannot launch a new waiting process while another waiting process is running. The id of the running process is ${runningId}.
Please wait until this process is complete (you can use read-process tool with wait=true for this purpose), or launch the new process as a background process with \`wait=false\`.
Do not kill this process unless you think it is stuck or is a server you want to stop.
Here is the output from the running process ${runningId}):
<output>${output?.output ?? ""}</output>`;
            }
        }

        let lastCommand = "";
        if (terminal === TerminalProcessTools._longRunningTerminal && !isNewTerminal) {
            // We need to send a noop command to make sure we're at the prompt
            let noop = "";
            if (!isVsCodeVersionGte("1.98.0") || !this._isTerminalBasicallySupported()) {
                noop = this.shellName === "powershell" ? "#" : ":";
            }

            const success =
                await TerminalProcessTools._completionStrategy!.runCommandUntilCompletion(
                    terminal,
                    noop, // command
                    "noop", // commandName
                    AgentSessionEventName.vsCodeTerminalTimedOutWaitingForNoopCommand // timeoutEventName
                );
            if (success) {
                lastCommand = noop;
            } else {
                // Something wrong with the current terminal, dispose it and create a new one.
                terminal.dispose();
                terminal = await this._createTerminal(`Augment - ${command}`, cwd);
                isNewTerminal = true;
                TerminalProcessTools._longRunningTerminal = terminal;
            }

            // Set the provided cwd if it's different from the current cwd
            const currentCwd = this._getCurrentCwd(terminal);
            if (cwd && currentCwd !== cwd) {
                const escapedCwd = cwd.replace(/"/g, '\\"');
                const setCwdCommand = `cd "${escapedCwd}"`;
                this._logger.debug(`Current cwd is ${currentCwd}, Setting cwd to ${cwd}`);
                const success =
                    await TerminalProcessTools._completionStrategy!.runCommandUntilCompletion(
                        terminal,
                        // this command should be the same regardless of the shell and platform
                        setCwdCommand, // command
                        "setCwd", // commandName
                        AgentSessionEventName.vsCodeTerminalTimedOutWaitingForSetCwdCommand // timeoutEventName
                    );
                if (success) {
                    lastCommand = setCwdCommand;
                } else {
                    // Something wrong with the current terminal, dispose it and create a new one.
                    terminal.dispose();
                    terminal = await this._createTerminal(`Augment - ${command}`, cwd);
                    isNewTerminal = true;
                    TerminalProcessTools._longRunningTerminal = terminal;
                }
            }
        }

        // Use completion strategy to wrap the command
        const actualCommand = TerminalProcessTools._completionStrategy!.wrapCommand(
            command,
            id,
            terminal,
            true
        );

        const processInfo: TerminalProcess = {
            terminal,
            command,
            actualCommand,
            lastCommand,
            output: "",
            state: ProcessStatus.running,
            readStream: undefined,
            execution: undefined,
            exitCode: null,
            toolUseId,
            seenStartEvent: false,
        };
        this._processes.set(id, processInfo);

        let scriptSessionActive = false;
        if (TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy) {
            // Check if script session is still active and reinitialize if needed
            // This serves as a safety net in case the script session dies unexpectedly
            // (user manually exits, system issues, or if our selective killing fails)
            scriptSessionActive =
                await TerminalProcessTools._completionStrategy.ensureTerminalSessionActive(
                    terminal,
                    this.shellName
                );

            // Start background polling for ScriptCaptureStrategy
            this._pollingManager?.startPolling();
        }

        /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */
        // If we're using the long-running terminal, shell integration may already exist.
        if (!scriptSessionActive && this._terminalHasShellIntegration(terminal)) {
            const execution = (terminal as any).shellIntegration.executeCommand(actualCommand);
            processInfo.execution = execution;
            processInfo.readStream = execution.read();
            this._logger.debug(`Using existing shell integration for command: ${actualCommand}`);
        } else if (
            !scriptSessionActive &&
            this._isTerminalBasicallySupported() &&
            (vscode.window as any).onDidChangeTerminalShellIntegration
        ) {
            // We have to wait for shell integration to be enabled.
            let shellIntegrationListener: vscode.Disposable | undefined;
            shellIntegrationListener = (vscode.window as any).onDidChangeTerminalShellIntegration(
                (e: any) => {
                    if (e.terminal === terminal && e.shellIntegration && !processInfo.readStream) {
                        const execution = e.shellIntegration.executeCommand(actualCommand);
                        processInfo.execution = execution;
                        processInfo.readStream = execution.read();
                        this._logger.debug(`Using shell integration for command: ${actualCommand}`);
                        if (shellIntegrationListener) {
                            shellIntegrationListener.dispose();
                            shellIntegrationListener = undefined;
                        }
                    }
                }
            );
            // Fallback to sendText if there is no shell integration within 2 seconds of launching.
            // Yes, this is the official way to to use the API.
            setTimeout(() => {
                if (!processInfo.readStream) {
                    terminal.sendText(actualCommand);
                    this._logger.debug(
                        `Failed to use shell integration for command: ${actualCommand}`
                    );
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalFailedToUseShellIntegration,
                        conversationId: "",
                    });
                }
                if (shellIntegrationListener) {
                    shellIntegrationListener.dispose();
                    shellIntegrationListener = undefined;
                }
            }, 2000);
        } else {
            this._logger.debug(`Not using shell integration for command: ${actualCommand}`);
            terminal.sendText(actualCommand);
        }
        /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

        // Handle abort signal
        abortSignal.addEventListener("abort", () => {
            void this.kill(id);
        });

        return id;
    }

    /**
     * Creates a terminal with consistent configuration
     */
    private async _createTerminal(name: string, cwd: string | undefined): Promise<vscode.Terminal> {
        /* eslint-disable @typescript-eslint/naming-convention */
        this._logger.verbose(`Creating terminal with name: ${name}, cwd: ${cwd}`);
        let env: Record<string, string> | undefined =
            this.shellName === "bash" || this.shellName === "zsh" || this.shellName === "fish"
                ? {
                      PAGER: "cat",
                      LESS: "-FX",
                      GIT_PAGER: "cat",
                  }
                : this.shellName === "powershell"
                  ? {
                        GIT_PAGER: "",
                    }
                  : {};
        /* eslint-enable @typescript-eslint/naming-convention */
        // Add any shell-specific environment variables from shellInfo
        if (TerminalProcessTools._shellInfo.env) {
            env = { ...env, ...TerminalProcessTools._shellInfo.env };
        }

        let shellPath = TerminalProcessTools._shellInfo.path ?? this.shellName;

        if (TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy) {
            // IMPORTANT: Set SHELL to the actual shell path
            // This ensures script command uses the correct shell
            shellPath =
                TerminalProcessTools._shellInfo.path ??
                this._findShellPath(this.shellName) ??
                this.shellName;

            this._logger.verbose(
                `shellPath resolved to: ${shellPath}, _shellInfo.path: ${TerminalProcessTools._shellInfo.path}, shellName: ${this.shellName}`
            );

            env = {
                ...env,
                ["SHELL"]: shellPath,
            };
        }

        // If no environment variables are set, use undefined instead of an empty object
        if (env && Object.keys(env).length === 0) {
            env = undefined;
        }

        const terminal = vscode.window.createTerminal({
            name,
            shellPath: shellPath,
            shellArgs: TerminalProcessTools._shellInfo.args,
            cwd,
            env,
            iconPath: {
                light: vscode.Uri.joinPath(this._extensionRoot, "media", "panel-icon-light.svg"),
                dark: vscode.Uri.joinPath(this._extensionRoot, "media", "panel-icon-dark.svg"),
            },
            isTransient: true,
        });

        // Set up completion detection using the strategy
        await TerminalProcessTools._completionStrategy!.setupTerminal(
            terminal,
            this.shellName,
            this._terminalSettings.startupScript?.trim() ?? ""
        );

        return terminal;
    }

    private _findShellPath(shellName: string): string | undefined {
        // Fallback: try to find using 'which' command
        try {
            //TODO (shawn): figure out what to use on windows. `which` only works on linux/mac.
            const result = spawnSync("which", [shellName], {
                encoding: "utf8",
                timeout: 5000,
            });

            if (result.status === 0 && result.stdout) {
                return result.stdout.trim();
            }

            this._logger.debug(`'which ${shellName}' command failed with status ${result.status}`);
            return undefined;
        } catch (error) {
            this._logger.error(
                `Failed to find shell path for ${shellName} using 'which' command: ${String(error)}`
            );
            return undefined;
        }
    }

    public async kill(
        id: number
    ): Promise<{ output: string; killed: boolean; returnCode: number | null } | undefined> {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.kill(id);
        }

        const process = this._processes.get(id);
        if (!process) {
            return undefined;
        }
        if (process.state === ProcessStatus.killed) {
            return { output: process.output, killed: false, returnCode: process.exitCode };
        }

        this._logger.verbose(`Killing process ${id}`);

        if (process.terminal === TerminalProcessTools._longRunningTerminal) {
            // For long-running terminal, send Ctrl+C to interrupt the current command
            // This should interrupt the command without killing the script session
            this._logger.debug(
                `Sending Ctrl+C to interrupt current command in long-running terminal`
            );
            TerminalProcessTools._longRunningTerminal.sendText("\x03", false);
        } else {
            process.terminal.dispose();
        }

        process.state = ProcessStatus.killed;
        process.exitCode = -1;
        process.output = await this._getOutputFromPossiblyIncompleteProcess(process, id);

        return { output: process.output, killed: true, returnCode: process.exitCode };
    }

    public isInLongRunningTerminal(id: number): boolean {
        // "exec" shells don't use long-running terminals
        if (this._isExecShell()) {
            return false;
        }

        const process = this._processes.get(id);
        return !!process && process.terminal === TerminalProcessTools._longRunningTerminal;
    }

    /**
     * Get the optimal timeout for a command, choosing between predicted and requested timeout
     */
    public async getOptimalTimeout(command: string, requestedTimeout: number): Promise<number> {
        return await this._timeoutPredictor.getOptimalTimeout(command, requestedTimeout);
    }

    /**
     * Wait for process with execution time tracking
     */
    public async waitForProcessWithTracking(
        id: number,
        timeoutSeconds: number,
        abortSignal: AbortSignal
    ): Promise<{ output: string; returnCode: number | null; status: ProcessStatus }> {
        const process = this._processes.get(id);
        if (!process) {
            return { output: "", returnCode: null, status: ProcessStatus.running };
        }

        const startTime = Date.now();

        try {
            const result = await this.waitForProcess(id, timeoutSeconds, abortSignal);

            // Record execution
            const executionTimeSeconds = (Date.now() - startTime) / 1000;
            const timedOut = process?.state === ProcessStatus.running;

            void this._timeoutPredictor.recordExecution(
                process.command,
                timedOut ? null : executionTimeSeconds,
                timeoutSeconds
            );

            // Return the result with process status
            return {
                output: result.output,
                returnCode: result.returnCode,
                status: process.state,
            };
        } catch (error) {
            // Record failed execution (likely timed out)
            void this._timeoutPredictor.recordExecution(process.command, null, timeoutSeconds);
            throw error;
        }
    }

    /**
     * Read output directly from terminal without using ScriptCaptureStrategy
     * Used as fallback when ScriptCaptureStrategy doesn't have output available
     */
    private async readOutput(
        id: number
    ): Promise<{ output: string; returnCode: number | null } | undefined> {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.readOutput(id);
        }

        const process = this._processes.get(id);
        if (!process) {
            return undefined;
        }

        // For completed processes, return stored output
        if (process.state === ProcessStatus.killed) {
            return {
                output: process.output,
                returnCode: process.exitCode,
            };
        }

        this._logger.verbose(`Reading output for process ${id}`);
        const output = await this._getOutputFromPossiblyIncompleteProcess(process, id);

        return {
            output,
            returnCode: process.exitCode,
        };
    }

    // Read directly from script output log file for ScriptCapture strategy and then fallback to reading from terminal.
    public async hybridReadOutput(
        id: number
    ): Promise<{ output: string; returnCode: number | null } | undefined> {
        const process = this._processes.get(id);
        if (!process) {
            return undefined;
        }

        // For ScriptCaptureStrategy, try to get output from script capture first
        if (
            TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy &&
            TerminalProcessTools._completionStrategy.isReady(process.terminal)
        ) {
            const strategyOutput =
                TerminalProcessTools._completionStrategy.getOutputAndReturnCode?.(
                    id,
                    process.terminal,
                    process.actualCommand,
                    process.state === ProcessStatus.completed
                );

            if (strategyOutput) {
                // Use our helper method to truncate the output
                const truncatedOutput = await this._truncateOutput(
                    strategyOutput.output ?? "",
                    process.toolUseId,
                    process.command
                );
                return { output: truncatedOutput, returnCode: strategyOutput.returnCode };
            }
            // If strategy doesn't have output, fall through to terminal reading
            this._logger.debug(
                `Script output log file is empty for process ${id}, falling through to regular timeout logic`
            );
        }

        // Fall back to reading directly from terminal
        return this.readOutput(id);
    }

    public writeInput(id: number, input: string): boolean {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.writeInput(id, input);
        }

        const process = this._processes.get(id);
        if (!process || process.state === ProcessStatus.killed || process.exitCode === -1) {
            return false;
        }

        const wrappedCommand = TerminalProcessTools._completionStrategy!.wrapCommand(
            input,
            id,
            process.terminal,
            false
        );
        process.terminal.sendText(wrappedCommand);
        process.actualCommand = wrappedCommand;
        return true;
    }

    public getProcessStatus(id: number): ProcessStatus | undefined {
        const process = this._processes.get(id);
        return process?.state;
    }

    public listProcesses(): {
        id: number;
        command: string;
        state: "running" | "completed" | "killed";
        returnCode: number | null;
    }[] {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.listProcesses();
        }

        const result = [];
        for (const [id, process] of this._processes.entries()) {
            let state: "running" | "completed" | "killed";

            if (TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy) {
                state = process.state;
            } else {
                state =
                    process.state === ProcessStatus.completed
                        ? "completed"
                        : process.state === ProcessStatus.killed || process.exitCode !== null
                          ? process.exitCode === -1
                              ? "killed"
                              : "completed"
                          : "running";
            }

            result.push({
                id,
                command: process.command,
                state,
                returnCode: process.exitCode,
            });
        }
        return result;
    }

    /**
     * Updates process states from background polling
     * Called by ProcessPollingManager to check for completion of active processes
     */
    public updateProcessStatesFromPolling(): void {
        if (this._isExecShell() && this._shellProcessTools) {
            // For exec shells, delegate to ShellProcessTools
            return;
        }

        const activeProcesses = Array.from(this._processes.entries()).filter(
            ([_, process]) => process.state === ProcessStatus.running && process.exitCode === null
        );

        if (activeProcesses.length === 0) {
            // No active processes, stop polling
            this._logger.debug("No active processes, stopping background polling");
            this._pollingManager?.stopPolling();
            return;
        }

        this._logger.debug(
            `Background polling checking ${activeProcesses.length}  active processes`
        );

        // Reuse the single process completion check for each process
        for (const [id, _] of activeProcesses) {
            this._checkSingleProcessCompletion(id, "background polling");
        }
    }

    /**
     * Checks completion status for a single process
     * Used for immediate checks after writeInput operations and background polling
     * @param id - Process ID to check
     * @param context - Optional context for logging (e.g., "writeInput", "background polling")
     */
    private _checkSingleProcessCompletion(id: number, context: string = "writeInput"): void {
        if (this._isExecShell() && this._shellProcessTools) {
            // For exec shells, delegate to ShellProcessTools
            return;
        }

        if (!(TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy)) {
            return;
        }

        const process = this._processes.get(id);
        if (!process || process.state !== ProcessStatus.running || process.exitCode !== null) {
            return; // Process not found or already completed
        }

        // For background polling, skip if a process check is already in progress to avoid race conditions
        if (TerminalProcessTools._completionStrategy.isProcessCheckInProgress(process.terminal)) {
            this._logger.debug(
                `Process check already in progress for process ${id}, skipping to avoid race condition`
            );
            return;
        }

        try {
            // Use the completion strategy to check if process has completed
            const completionResult = TerminalProcessTools._completionStrategy.checkCompleted(
                id,
                process.terminal
            );

            if (completionResult.isCompleted) {
                process.state = ProcessStatus.completed;
                // For non-long-running terminals, dispose them.
                // For long-running terminals, we just mark the process as complete.
                if (process.terminal !== TerminalProcessTools._longRunningTerminal) {
                    process.terminal.dispose();
                }
            }
        } catch (error) {
            this._logger.debug(
                `Error in ${context} checking completion for process ${id}: ${String(error)}`
            );
        }
    }

    public canShowTerminal(id: number): boolean {
        // If using "exec" shell, there's no terminal to show
        if (this._isExecShell()) {
            return false;
        }

        const process = this._processes.get(id);
        if (!process) {
            return false;
        }

        return process.terminal.exitStatus === undefined;
    }

    public canShowTerminalByCommand(command: string): boolean {
        // If using "exec" shell, there's no terminal to show
        if (this._isExecShell()) {
            return false;
        }

        // Find the most recent process with this command
        for (const [, process] of Array.from(this._processes).reverse()) {
            if (process.command === command && process.state !== ProcessStatus.killed) {
                return true;
            }
        }

        return false;
    }

    public showTerminal(id: number): boolean {
        // If using "exec" shell, there's no terminal to show
        if (this._isExecShell()) {
            return false;
        }

        const process = this._processes.get(id);
        if (!process || process.terminal.exitStatus !== undefined) {
            return false;
        }

        process.terminal.show(true);
        return true;
    }

    public showTerminalByCommand(command: string): boolean {
        // If using "exec" shell, there's no terminal to show
        if (this._isExecShell()) {
            return false;
        }

        // Find the most recent process with this command
        for (const [, process] of Array.from(this._processes).reverse()) {
            if (process.command === command && process.state !== ProcessStatus.killed) {
                process.terminal.show(true);
                return true;
            }
        }

        return false;
    }

    public waitForProcess(
        id: number,
        timeoutSeconds: number,
        abortSignal: AbortSignal
    ): Promise<{ output: string; returnCode: number | null }> {
        // If using "exec" shell, delegate to ShellProcessTools
        if (this._isExecShell() && this._shellProcessTools) {
            return this._shellProcessTools.waitForProcess(id, timeoutSeconds, abortSignal);
        }

        return new Promise((resolve) => {
            void (async () => {
                const process = this._processes.get(id);
                if (!process) {
                    resolve({ output: "", returnCode: null });
                    return;
                }

                // If process already completed, return immediately
                if (process.exitCode !== null) {
                    resolve({
                        output: process.output,
                        returnCode: process.exitCode,
                    });
                    return;
                }

                const timer = setTimeout(() => {
                    void (async () => {
                        // Process still running after timeout
                        this._logger.verbose(
                            `Process ${id} still running after ${timeoutSeconds} seconds.  Timing out.`
                        );
                        getAgentSessionEventReporter().reportEvent({
                            eventName: AgentSessionEventName.vsCodeTerminalWaitTimeout,
                            conversationId: "",
                        });
                        const result = await this.hybridReadOutput(id);

                        this._waitResolvers.delete(id);
                        if (this._activePoller) {
                            clearInterval(this._activePoller);
                            this._activePoller = undefined;
                        }
                        resolve({
                            output: result?.output ?? "",
                            returnCode: null,
                        });
                    })();
                }, timeoutSeconds * 1000);

                this._logger.verbose(`Waiting for process ${id} to complete.`);

                // As a backup, we poll to see if the process has completed.
                // This may be needed if we cannot listen for onDidEndTerminalShellExecution or if it
                // somehow missed the event.
                if (process.lastCommand === process.actualCommand) {
                    // Unfortunately, if the last command is the same as the current one and we don't
                    // know if we'll get the completion events, we don't have a way to know when the
                    // process has completed.  So we just show the model the current output and let
                    // it figure out what to do.  This is a heuristic that prioritizes the common
                    // case. It isn't perfect but we don't live in a perfect world.
                    this._logger.verbose(`Last command is the same as the current one.`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalLastCommandIsSameAsCurrent,
                        conversationId: "",
                    });
                    const result = await this.hybridReadOutput(id);
                    if (!result) {
                        this._logger.debug(`Failed to read output for process ${id}`);
                        getAgentSessionEventReporter().reportEvent({
                            eventName: AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                            conversationId: "",
                        });
                        resolve({ output: "", returnCode: null });
                    } else {
                        resolve(result);
                    }
                } else {
                    if (this._activePoller) {
                        clearInterval(this._activePoller);
                        this._activePoller = undefined;
                    }

                    // Use strategy-based polling if not using VSCode events
                    if (
                        TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy &&
                        TerminalProcessTools._completionStrategy.isReady(process.terminal)
                    ) {
                        this._logger.debug(`Using strategy-based polling for process ${id}`);

                        this._activePoller = setInterval(() => {
                            void (async () => {
                                this._logger.verbose(
                                    `Strategy-based polling to see if process ${id} running command ${process.actualCommand} is done.`
                                );

                                // Reuse the single process completion check
                                this._checkSingleProcessCompletion(id, "strategy-based polling");

                                // Check if the process is now in a finished state
                                const isFinished =
                                    process.state !== ProcessStatus.running ||
                                    process.exitCode !== null;

                                if (isFinished) {
                                    this._logger.info(
                                        `Strategy-based polling determined process ${id} is done.`
                                    );
                                    getAgentSessionEventReporter().reportEvent({
                                        eventName:
                                            AgentSessionEventName.vsCodeScriptStrategyPollingDeterminedProcessIsDone,
                                        conversationId: "",
                                    });
                                    clearTimeout(timer);
                                    clearInterval(this._activePoller);
                                    this._activePoller = undefined;

                                    const result = await this.hybridReadOutput(id);
                                    process.output = result?.output ?? "";
                                    if (process.exitCode === null) {
                                        process.exitCode = result?.returnCode ?? null;
                                    }

                                    if (!process.output && process.exitCode === null) {
                                        this._logger.info(
                                            `Failed to read output for process ${id}`
                                        );
                                        getAgentSessionEventReporter().reportEvent({
                                            eventName:
                                                AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                                            conversationId: "",
                                        });
                                        resolve({ output: "", returnCode: null });
                                        return;
                                    } else {
                                        resolve({
                                            output: process.output,
                                            returnCode: process.exitCode,
                                        });
                                    }
                                }
                            })();
                        }, 1000);
                    } else {
                        this._logger.debug(
                            `Fallback to Using clipboard-based polling for process ${id}`
                        );
                        // Fallback to original clipboard-based polling
                        this._activePoller = setInterval(() => {
                            void (async () => {
                                this._logger.verbose(`Polling to see if process ${id} is done.`);
                                const { lastCommand, isComplete } = await this.getLastCommand(
                                    process.terminal
                                );
                                // For multi-line commands, we can't directly check if the commands
                                // match, as the version in the terminal may have extra characters
                                // (e.g., extra lines can start with >).  As a heuristic, we thus check
                                // if the first lines match.
                                const firstLinesMatch =
                                    lastCommand.split("\n")[0] ===
                                    process.actualCommand.split("\n")[0];
                                if (isComplete || (isComplete === undefined && firstLinesMatch)) {
                                    this._logger.debug(
                                        `Polling determined process ${id} is done with ${isComplete} and ${firstLinesMatch}.`
                                    );
                                    getAgentSessionEventReporter().reportEvent({
                                        eventName:
                                            AgentSessionEventName.vsCodeTerminalPollingDeterminedProcessIsDone,
                                        conversationId: "",
                                    });
                                    clearTimeout(timer);
                                    clearInterval(this._activePoller);
                                    this._activePoller = undefined;
                                    const result = await this.readOutput(id);
                                    process.output = result?.output ?? "";
                                    process.exitCode = result?.returnCode ?? null;
                                    process.state = ProcessStatus.killed;
                                    if (!result) {
                                        this._logger.debug(
                                            `Failed to read output for process ${id}`
                                        );
                                        getAgentSessionEventReporter().reportEvent({
                                            eventName:
                                                AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                                            conversationId: "",
                                        });
                                        resolve({ output: "", returnCode: null });
                                    } else {
                                        resolve(result);
                                    }
                                }
                            })();
                        }, 1000);
                    }
                    // Store resolver to be called when process completes
                    this._waitResolvers.set(id, (result) => {
                        clearTimeout(timer);
                        clearInterval(this._activePoller);
                        this._activePoller = undefined;
                        this._waitResolvers.delete(id);
                        resolve(result);
                    });
                }

                const abortHandler = () => {
                    clearTimeout(timer);
                    clearInterval(this._activePoller);
                    this._activePoller = undefined;
                    this._waitResolvers.delete(id);
                    resolve({ output: "", returnCode: null });
                };
                void abortSignal.addEventListener("abort", abortHandler);
            })();
        });
    }

    private async _getOutputFromPossiblyIncompleteProcess(process: TerminalProcess, id: number) {
        if (process.readStream) {
            const rawOutput = await this._readProcessStreamWithTimeout(process.readStream, id);
            if (rawOutput === undefined) {
                // Timed out without reading any data, fall back to clipboard
                this._logger.debug(
                    `Process ${id} timed out without reading data in incomplete process, falling back to clipboard`
                );
                return await this._getOutputFromClipboard(id);
            }
            const cleanOutput = _stripControlCodes(rawOutput);

            // Use our helper method to truncate the output with the tool use ID if available
            const result = await this._truncateOutput(
                cleanOutput,
                process.toolUseId,
                process.command
            );
            this._logger.debug(
                `Reading exact intermediate output for process ${id} got ${result.length} character(s).`
            );
            return result;
        } else {
            return await this._getOutputFromClipboard(id);
        }
    }

    private async _readProcessStreamWithTimeout(
        readStream: AsyncIterable<string>,
        id: number
    ): Promise<string | undefined> {
        let rawOutput = "";
        let hasReadData = false;
        try {
            // Use a non-blocking approach to read only currently available output
            // We'll create a function to read a single chunk with a timeout
            const readChunkWithTimeout = async (
                iterator: AsyncIterator<string>
            ): Promise<IteratorResult<string>> => {
                let timeoutId: NodeJS.Timeout | undefined;
                let isResolved = false;

                // Create a promise that resolves after a short timeout
                const timeoutPromise = new Promise<IteratorResult<string>>((resolve) => {
                    timeoutId = setTimeout(() => {
                        if (!isResolved) {
                            isResolved = true;
                            this._logger.debug(`Read timeout occurred for process ${id}`);
                            resolve({ done: true, value: undefined });
                        }
                    }, 100);
                });

                // Race between getting the next chunk and timing out
                const result = await Promise.race([
                    iterator.next().then((result) => {
                        if (!isResolved) {
                            isResolved = true;
                            // Clear the timeout to prevent it from executing later
                            if (timeoutId) {
                                clearTimeout(timeoutId);
                            }
                        }
                        return result;
                    }),
                    timeoutPromise,
                ]);

                return result;
            };

            // Get the iterator from the AsyncIterable
            const iterator = readStream[Symbol.asyncIterator]();

            // Read chunks until we timeout (no more immediate data)
            let result = await readChunkWithTimeout(iterator);
            while (!result.done) {
                if (result.value !== undefined) {
                    rawOutput += result.value;
                    hasReadData = true;
                }
                result = await readChunkWithTimeout(iterator);
            }
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error reading stream for process ${id}: ${e.message ?? ""}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalFailedToReadOutput,
                conversationId: "",
            });
        }

        // Return undefined if we timed out without reading any data
        if (!hasReadData) {
            this._logger.debug(
                `Process ${id} timed out without reading any data, returning undefined`
            );
            return undefined;
        }

        return rawOutput;
    }

    // Note that this works even if the terminal is not shown.
    private async _getOutputFromClipboard(id: number) {
        const process = this._processes.get(id);
        const terminal = process?.terminal;
        if (
            terminal &&
            terminal === TerminalProcessTools._longRunningTerminal &&
            !this._terminalHasShellIntegration(terminal)
        ) {
            return (await this._getLastCommandAndOutputFallback(terminal)).output;
        }
        getAgentSessionEventReporter().reportEvent({
            eventName: AgentSessionEventName.vsCodeTerminalReadingApproximateOutput,
            conversationId: "",
        });

        const oldClipboard = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText("");
        await vscode.commands.executeCommand("workbench.action.terminal.copyLastCommandOutput");
        const output = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText(oldClipboard);
        this._logger.verbose(`Read approximate output from clipboard got ${output.trim()}`);

        // Use our helper method to truncate the output
        return await this._truncateOutput(output, id.toString(), process?.command);
    }

    // Note that this works even if the terminal is not shown.
    public async getLastCommand(terminal?: vscode.Terminal) {
        if (
            terminal &&
            terminal === TerminalProcessTools._longRunningTerminal &&
            !this._terminalHasShellIntegration(terminal)
        ) {
            // Use fallback method (requires terminal to be shown)
            const {
                command,
                output: _output,
                isComplete,
            } = await this._getLastCommandAndOutputFallback(terminal);
            return { lastCommand: command, isComplete };
        }

        const oldClipboard = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText("");
        await vscode.commands.executeCommand("workbench.action.terminal.copyLastCommand");
        const lastCommand = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText(oldClipboard);
        this._logger.verbose(`Read last command from clipboard got ${lastCommand.trim()}`);
        return { lastCommand };
    }

    /**
     * VSCode's terminal APIs for copying the last command and output
     * (workbench.action.terminal.copy*) don't seem to work when shell integrations are not
     * enabled.  So we use a fallback that seems to work more reliably.
     *
     * Note that this only works if the terminal is shown.
     */
    private async _getLastCommandAndOutputFallback(terminal: vscode.Terminal) {
        // This only works when the terminal is shown, so we need to show it.
        terminal.show();

        const oldClipboard = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText("");
        await vscode.commands.executeCommand("workbench.action.terminal.selectToPreviousCommand");
        await vscode.commands.executeCommand("workbench.action.terminal.copySelection");
        const lastCommandAndOutput = await vscode.env.clipboard.readText();
        await vscode.commands.executeCommand("workbench.action.terminal.selectAll");
        await vscode.commands.executeCommand("workbench.action.terminal.copyAndClearSelection");
        const allOutput = await vscode.env.clipboard.readText();
        await vscode.env.clipboard.writeText(oldClipboard);

        // Extract the command and the output.
        // Match common shell prompts more accurately
        // PowerShell: typically "PS path>" or just "PS>"
        // Bash: typically includes username@hostname:path$ or just "$"
        // Zsh: typically includes username@hostname:path% or just "%"
        // Default: catch other prompts with a generic pattern
        const prompt = _shellPrompts[this.shellName] ?? _shellPrompts["bash"];

        const firstLine = lastCommandAndOutput.split("\n").shift() ?? "";
        const command = firstLine.replace(prompt, "").trim();

        // Get the raw output by removing the first line (command)
        const rawOutput = lastCommandAndOutput.slice(firstLine.length + 1);
        // Remove prompt lines that might appear at the end of the output
        // Split by newlines, filter out lines that match the prompt pattern, and join back
        const outputLines = rawOutput.split("\n");
        const filteredLines = outputLines.filter((line) => !prompt.test(line));
        const output = filteredLines.join("\n");

        // Check whether the last non-empty line is a full prompt (with nothing after it).
        // Note that sometimes the result of `selectToPreviousCommand` will not contain the prompt
        // at the end (if the terminal is small enough or scrolled up and it's not onscreen?), so
        // we instead check the last non-empty line in the entire terminal output.
        const lastNonEmptyLine = allOutput
            .split("\n")
            .reverse()
            .find((line) => line.trim().length > 0);
        const isComplete =
            lastNonEmptyLine != null && lastNonEmptyLine.replace(prompt, "").trim().length === 0;

        this._logger.verbose(
            `Reading last command and output from clipboard got ${lastCommandAndOutput.trim()}, command: ${command}, output: ${output.trim()}, isComplete: ${isComplete}`
        );
        return { command, output, isComplete };
    }

    public closeAllProcesses() {
        // Clean up shell processes if using "exec" shell
        if (this._shellProcessTools) {
            this._shellProcessTools.closeAllProcesses();
        }

        // Clean up all terminal processes
        for (const process of this._processes.values()) {
            if (process.state === ProcessStatus.running) {
                if (process.terminal === TerminalProcessTools._longRunningTerminal) {
                    // Send SIGINT (Ctrl+C) to terminate the current process.
                    // We keep the terminal open so users can see the output.
                    this._logger.debug(
                        `closeAllProcesses: Sending Ctrl+C to interrupt current command in long-running terminal`
                    );
                    TerminalProcessTools._longRunningTerminal.sendText("\x03", false);
                } else {
                    // For regular terminals, just dispose them directly
                    process.terminal.dispose();
                }
            }
        }
        this._processes.clear();

        if (this._activePoller) {
            clearInterval(this._activePoller);
            this._activePoller = undefined;
        }
    }

    public cleanup() {
        this._logger.debug(`Cleaning up terminal process tools`);
        this._isDisposed = true;
        this.closeAllProcesses();

        // Clean up background polling
        this._pollingManager?.dispose();

        // Clean up any temporary directories
        void this._cleanupTempDirectories();
        this.dispose();
    }

    /**
     * Cleans up any temporary directories created for shells
     */
    private async _cleanupTempDirectories(): Promise<void> {
        // Clean up temporary directories for all shells
        for (const shell of this._supportedShells) {
            if (shell.shellInfo.tempDir) {
                try {
                    await deleteDirectory(shell.shellInfo.tempDir);
                } catch (error: any) {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    this._logger.debug(`Error cleaning up temporary directory: ${error.message}`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalErrorCleaningUpTempDir,
                        conversationId: "",
                    });
                }
            }
        }

        // Also clean up the current shell's temp directory if it exists
        if (TerminalProcessTools._shellInfo.tempDir) {
            try {
                await deleteDirectory(TerminalProcessTools._shellInfo.tempDir);
            } catch (error: any) {
                this._logger.debug(
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    `Error cleaning up current shell temporary directory: ${error.message}`
                );
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalErrorCleaningUpTempDir,
                    conversationId: "",
                });
            }
        }
    }

    /**
     * Gets the current capability result for the active shell
     */
    private get _currentCapability(): TerminalCapabilityResult {
        if (this._supportedShells.length > 0) {
            // Find the capability for the current shell
            const current = this._supportedShells.find(
                (s) =>
                    s.shellInfo.name === TerminalProcessTools._shellInfo.name &&
                    s.shellInfo.path === TerminalProcessTools._shellInfo.path
            );

            if (current) {
                return current.capability;
            }
        }

        // Default to unknown if we don't have capability information
        return {
            capability: TerminalCapability.unknown,
            details: "Capability information not available",
        };
    }

    /**
     * Checks if the terminal has basic support (may have issues but is usable)
     */
    private _isTerminalBasicallySupported(): boolean {
        const capability = this._currentCapability.capability;
        return this._isBasicallySupported(capability);
    }

    // We consider a terminal to be basically supported if it has shell integration and execution events,
    // even if it has output issues or buggy execution events.
    private _isBasicallySupported(capability: TerminalCapability): boolean {
        return (
            capability === TerminalCapability.supported ||
            capability === TerminalCapability.buggyOutput ||
            capability === TerminalCapability.noisyOutput ||
            capability === TerminalCapability.noOutput ||
            capability === TerminalCapability.buggyExecutionEvents
        );
    }

    /**
     * Get the current working directory for a terminal, preferring CWD tracking
     * over potentially stale shellIntegration.cwd when using ScriptCaptureStrategy
     */
    private _getCurrentCwd(terminal: vscode.Terminal): string {
        let currentCwd = "";

        // When using ScriptCaptureStrategy, prefer CWD tracking file since shellIntegration.cwd
        // becomes stale after script session starts (cd commands don't update it)
        if (TerminalProcessTools._completionStrategy instanceof ScriptCaptureStrategy) {
            const trackedCwd = TerminalProcessTools._completionStrategy.getCurrentCwd(terminal);
            if (trackedCwd) {
                currentCwd = trackedCwd;
                this._logger.debug(`Using CWD from tracking file: ${currentCwd}`);
            }
        }
        if (currentCwd === "") {
            // For other strategies, use shellIntegration.cwd as primary source
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
            const currentCwdUri = (terminal as any).shellIntegration?.cwd;
            if (currentCwdUri instanceof vscode.Uri) {
                currentCwd = currentCwdUri.fsPath;
                this._logger.debug(`Using CWD from shellIntegration: ${currentCwd}`);
            }
        }
        return currentCwd;
    }

    /**
     * Gets the terminal info for the long running terminal.
     */
    public getLongRunningTerminalInfo(): TerminalInfo | undefined {
        // "exec" shells don't use long-running terminals
        if (this._isExecShell()) {
            return undefined;
        }

        // `shellIntegration` is required for this feature, and in the API since 1.93.
        if (!this._isTerminalBasicallySupported() || !TerminalProcessTools._longRunningTerminal) {
            return undefined;
        }

        const currentCwd = this._getCurrentCwd(TerminalProcessTools._longRunningTerminal);
        if (!currentCwd) {
            return undefined;
        }

        return {
            // NOTE(arun): For now, we only support a single long running terminal so
            // we can hardcode the ID to 0. In the future, we may want to support
            // multiple long running terminals, in which case we should use a different
            // ID for each one.
            // eslint-disable-next-line @typescript-eslint/naming-convention
            terminal_id: 0,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            current_working_directory: currentCwd,
        };
    }

    /**
     * Initializes the shells by finding all supported shells and setting the default shell
     */
    private async _initializeShells(): Promise<void> {
        const originalShellInfo = TerminalProcessTools._shellInfo;
        try {
            // Find all supported shells
            this._supportedShells = await this._findSupportedShells();

            // Set the default shell to the best one available
            if (this._supportedShells.length > 0) {
                const defaultProfile = this._getVSCodeDefaultProfile();
                // Sort by capability (best first), then by default profile (if any), but always put "exec" last
                this._supportedShells.sort((a, b) => {
                    // Always put "exec" shells last
                    if (
                        a.shellInfo.friendlyName === "exec" &&
                        b.shellInfo.friendlyName !== "exec"
                    ) {
                        return 1;
                    }
                    if (
                        b.shellInfo.friendlyName === "exec" &&
                        a.shellInfo.friendlyName !== "exec"
                    ) {
                        return -1;
                    }
                    // If both are "exec" or neither is "exec", sort normally

                    // First sort by capability
                    const capabilityDiff = b.capability.capability - a.capability.capability;
                    if (capabilityDiff !== 0) {
                        return capabilityDiff;
                    }
                    // If capabilities are equal, prefer the default profile
                    if (defaultProfile) {
                        if (
                            a.shellInfo.name === defaultProfile.name &&
                            b.shellInfo.name !== defaultProfile.name
                        ) {
                            return -1;
                        }
                        if (
                            b.shellInfo.name === defaultProfile.name &&
                            a.shellInfo.name !== defaultProfile.name
                        ) {
                            return 1;
                        }
                    }
                    // If neither is default or both have same relation to default, maintain original order
                    return 0;
                });

                // Save the supported shells to global state
                // Convert shells to the format expected by the UI
                // Filter out shells with undefined names and remove duplicates
                const uiShells = this._supportedShells
                    .map((s) => ({
                        name: s.shellInfo.name,
                        path: s.shellInfo.path,
                        friendlyName: s.shellInfo.friendlyName,
                        supportString:
                            s.capability.capability === TerminalCapability.supported
                                ? "Fully supported"
                                : this._isBasicallySupported(s.capability.capability)
                                  ? "Partially supported"
                                  : "Not supported",
                    }))
                    .filter(
                        (shell, index, self) =>
                            index === self.findIndex((s) => s.friendlyName === shell.friendlyName)
                    );

                // Save the supported shells to global state
                this._terminalSettings.supportedShells = uiShells;
                await this._globalState.save(
                    FileStorageKey.terminalSettings,
                    this._terminalSettings
                );

                // Try to load saved shell preference
                const savedShellName = this._terminalSettings.selectedShell;

                // If we have a saved shell preference and it's in the supported shells, use it
                if (savedShellName) {
                    const savedShell = this._supportedShells.find(
                        (s) => s.shellInfo.friendlyName === savedShellName
                    );
                    if (savedShell) {
                        TerminalProcessTools._shellInfo = savedShell.shellInfo;
                        this._logger.debug(
                            `Shell initialization complete. Using saved shell preference: ${TerminalProcessTools._shellInfo.friendlyName} (${TerminalCapability[savedShell.capability.capability]})`
                        );
                    } else {
                        // Saved shell not found, use the best available
                        TerminalProcessTools._shellInfo = this._supportedShells[0].shellInfo;
                        this._logger.debug(
                            `Shell initialization complete. Saved shell '${savedShellName}' not found, using best available: ${TerminalProcessTools._shellInfo.friendlyName} (${TerminalCapability[this._supportedShells[0].capability.capability]})`
                        );
                    }
                } else {
                    // No saved preference, use the best available
                    TerminalProcessTools._shellInfo = this._supportedShells[0].shellInfo;
                    this._logger.debug(
                        `Shell initialization complete. Using shell: ${TerminalProcessTools._shellInfo.friendlyName} (${TerminalCapability[this._supportedShells[0].capability.capability]})`
                    );
                }
            } else {
                // Fallback to default shell if no supported shells found
                TerminalProcessTools._shellInfo = TerminalProcessTools._getDefaultShell();
                this._logger.debug(
                    `Shell initialization complete. No supported shells found, using default: ${TerminalProcessTools._shellInfo.friendlyName}`
                );
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalNoSupportedShellsFound,
                    conversationId: "",
                });
            }
        } catch (error: any) {
            // If anything goes wrong, use the default shell
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error initializing shells: ${error.message}`);
            TerminalProcessTools._shellInfo = TerminalProcessTools._getDefaultShell();
            this._logger.debug(
                `Shell initialization complete with errors. Using default shell: ${TerminalProcessTools._shellInfo.friendlyName}`
            );
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorInitializingShells,
                conversationId: "",
            });
        }
        if (
            TerminalProcessTools._longRunningTerminal &&
            !areSameShellInfo(originalShellInfo, TerminalProcessTools._shellInfo)
        ) {
            this._terminalNeedsUpdate = true;
        }
    }

    /**
     * Gets all shells that should be checked for capabilities
     */
    private async _getAllShellsToCheck(): Promise<ShellInfo[]> {
        const shellsToCheck: ShellInfo[] = [];

        // Get the VSCode default profile
        const defaultShellInfo = this._getVSCodeDefaultProfile();
        if (defaultShellInfo) {
            shellsToCheck.push(defaultShellInfo);
        }

        // Get all VSCode profiles
        const profiles = this._getVSCodeProfiles();
        for (const profile of profiles) {
            // Skip if we already have this shell in the list
            if (shellsToCheck.some((s) => s.name === profile.name && s.path === profile.path)) {
                continue;
            }
            shellsToCheck.push(profile);
        }

        // Add special case for zsh with clean environment
        const zshShellInfo = await this._createZshShellInfo(profiles);
        if (zshShellInfo) {
            shellsToCheck.push(zshShellInfo);
        }

        // Add special case for bash with clean environment
        const bashShellInfo = await this._createBashShellInfo(profiles);
        if (bashShellInfo) {
            shellsToCheck.push(bashShellInfo);
        }

        // Add the "exec" shell type (always supported)
        shellsToCheck.push({
            name: getDefaultShell(process.platform),
            path: undefined,
            args: undefined,
            env: undefined,
            friendlyName: "exec",
            tempDir: undefined,
        });

        return shellsToCheck;
    }

    /**
     * Finds all supported shells and checks their capabilities
     */
    private async _findSupportedShells(): Promise<
        Array<{ shellInfo: ShellInfo; capability: TerminalCapabilityResult }>
    > {
        const supportedShells: Array<{
            shellInfo: ShellInfo;
            capability: TerminalCapabilityResult;
        }> = [];

        // Get all shells to check
        const shellsToCheck = await this._getAllShellsToCheck();

        // If VSCode just started, our capability checks can be flaky because VSCode is still
        // starting up, which can cause timeouts. We thus wait briefly before running the checks.
        const timeSinceEnable = Date.now() - this._enableStart;
        if (timeSinceEnable < 5000) {
            this._logger.debug(
                `First terminal initialization since VSCode startup, delaying shell capability checks`
            );
            await delayMs(5000 - timeSinceEnable);
        }

        // Check each shell's capabilities
        for (const shellInfo of shellsToCheck) {
            if (this._isDisposed) {
                this._logger.debug("Shell capability checking cancelled due to disposal");
                break;
            }

            // Special handling for "exec" shell - always mark as supported
            if (shellInfo.friendlyName === "exec") {
                this._logger.debug(`Adding exec shell as always supported`);
                supportedShells.push({
                    shellInfo,
                    capability: {
                        capability: TerminalCapability.supported,
                        details: "exec shell uses child_process and is always supported",
                    },
                });
                continue;
            }

            if (!isSupportedShell(shellInfo.name) && !shellInfo.name.includes("powershell")) {
                this._logger.debug(`Skipping unsupported shell: ${shellInfo.name}`);
                continue;
            }

            try {
                this._logger.debug(
                    `Checking capabilities for shell: ${shellInfo.name} ${shellInfo.path || ""} ${shellInfo.args?.join(" ") || ""}`
                );

                // Create a temporary capability checker for this shell
                const checker = new TerminalCapabilityChecker(shellInfo);
                const capability = await checker.checkCapabilities();
                checker.dispose();

                this._logger.debug(
                    `Shell ${shellInfo.name} capability: ${TerminalCapability[capability.capability]} - ${capability.details}`
                );

                supportedShells.push({ shellInfo, capability });
            } catch (error: any) {
                this._logger.debug(
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    `Error checking capabilities for shell ${shellInfo.name}: ${error.message}`
                );
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingShellCapability,
                    conversationId: "",
                });
            }
        }

        return supportedShells;
    }

    /**
     * Gets the default shell for the current platform
     */
    private static _getDefaultShell(): ShellInfo {
        const defaultShell = getDefaultShell(process.platform) as string;
        return {
            name: defaultShell,
            path: undefined,
            args: undefined,
            env: undefined,
            friendlyName: defaultShell + " (default)",
            tempDir: undefined,
        };
    }

    /**
     * Creates a ShellInfo for zsh with a clean environment
     * @param profiles The list of available shell profiles
     * @returns A ShellInfo for zsh with a clean environment, or undefined if zsh is not available
     */
    private async _createZshShellInfo(profiles: ShellInfo[]): Promise<ShellInfo | undefined> {
        if (process.platform !== "darwin" && process.platform !== "linux") {
            return undefined;
        }

        const zshPath = profiles.find((p) => p.name === "zsh")?.path;
        if (!zshPath) {
            return undefined;
        }

        try {
            // Create a temporary directory for zsh configuration
            const tempDir = await createZshTempEnvironment();
            return {
                name: "zsh",
                path: zshPath,
                args: undefined,
                env: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    ZDOTDIR: tempDir, // Set ZDOTDIR to our temp directory with empty .zshrc
                },
                friendlyName: "zsh (vanilla)",
                tempDir: tempDir, // Store the temp dir path for cleanup later
            };
        } catch (error: any) {
            this._logger.debug(
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                `Failed to create temporary zsh environment: ${error.message}`
            );
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCreatingZshEnvironment,
                conversationId: "",
            });
            return undefined;
        }
    }

    /**
     * Creates a ShellInfo for bash with a clean environment
     * @param profiles The list of available shell profiles
     * @returns A ShellInfo for bash with a clean environment, or undefined if bash is not available
     */
    private async _createBashShellInfo(profiles: ShellInfo[]): Promise<ShellInfo | undefined> {
        if (process.platform !== "darwin" && process.platform !== "linux") {
            return undefined;
        }

        const bashPath = profiles.find((p) => p.name === "bash")?.path;
        if (!bashPath) {
            return undefined;
        }

        // We need to invoke bash with --init-file <path to VSCode's shellIntegration-bash.sh>.
        // We find that by starting a hidden bash terminal and using ps to see how it was invoked.
        const tempTerminal = vscode.window.createTerminal({
            name: "augment-bash-test",
            shellPath: bashPath,
            hideFromUser: true,
            isTransient: true,
        });
        const pid = await tempTerminal.processId;
        const bashArgs = await executeCommand(`ps -p ${pid} -o args=`, {});
        tempTerminal.dispose();
        if (!bashArgs) {
            return undefined;
        }
        const argsParts = split(bashArgs);
        const initFileIndex = argsParts.findIndex((arg: string) => arg === "--init-file");
        if (initFileIndex === -1 || initFileIndex >= argsParts.length - 1) {
            return undefined;
        }
        const initFilePath = argsParts[initFileIndex + 1];

        return {
            name: "bash",
            path: bashPath,
            args: ["--init-file", initFilePath],
            friendlyName: "bash (vanilla)",
        };
    }

    /**
     * Gets the OS-specific section name for terminal configuration
     */
    private _getOSSection(): string {
        if (process.platform === "win32") {
            return "windows";
        } else if (process.platform === "darwin") {
            return "osx";
        } else {
            return "linux";
        }
    }

    /**
     * Processes shell info to handle special cases like Git Bash and PowerShell on Windows
     * @param shellInfo The shell info to process
     * @param logErrors Whether to log errors
     * @returns Processed shell info or undefined if processing failed
     */
    private _processShellInfo(
        shellInfo: { name: string; path: string | undefined; args: string[] | undefined },
        logErrors: boolean
    ): ShellInfo | undefined {
        // Windows uses "Git Bash" instead of "bash".
        if (shellInfo.name === "git bash" && process.platform === "win32") {
            const gitBashInfo = this._getGitBashInfo(shellInfo.path, shellInfo.args);
            if (!gitBashInfo) {
                if (logErrors) {
                    this._logger.debug(`Failed to find/use Git Bash path`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalFailedToFindGitBash,
                        conversationId: "",
                    });
                }
                return undefined;
            }
            shellInfo.name = gitBashInfo.name;
            shellInfo.path = gitBashInfo.path;
            shellInfo.args = gitBashInfo.args;
        }

        // Handle PowerShell on Windows
        // The name could be something like "PowerShell" or "PowerShell 7" or "Windows PowerShell"
        if (shellInfo.name.includes("powershell") && process.platform === "win32") {
            const powershellPath = this._findPowerShellPath();
            if (!powershellPath) {
                if (logErrors) {
                    this._logger.debug(`Failed to find PowerShell path`);
                    getAgentSessionEventReporter().reportEvent({
                        eventName: AgentSessionEventName.vsCodeTerminalFailedToFindPowerShell,
                        conversationId: "",
                    });
                }
                return undefined;
            }
            shellInfo.name = "powershell";
            shellInfo.path = powershellPath;
        }

        if (shellInfo.name && isSupportedShell(shellInfo.name)) {
            // If path is just the shell name, resolve it to full path
            let resolvedPath = shellInfo.path;
            if (resolvedPath === shellInfo.name) {
                // Path is just the shell name, resolve to full path using existing method
                resolvedPath = this._findShellPath(shellInfo.name);
            }

            return {
                ...shellInfo,
                path: resolvedPath,
                friendlyName: shellInfo.name,
            };
        } else if (shellInfo.name) {
            if (logErrors) {
                this._logger.debug(`Unsupported shell: ${shellInfo.name}`);
                getAgentSessionEventReporter().reportEvent({
                    eventName: AgentSessionEventName.vsCodeTerminalUnsupportedVSCodeShell,
                    conversationId: "",
                });
            }
            return undefined;
        }
    }

    /**
     * Gets the VSCode default profile
     */
    private _getVSCodeDefaultProfile(): ShellInfo | undefined {
        const section = this._getOSSection();
        const config = vscode.workspace.getConfiguration("terminal.integrated.defaultProfile");
        const configValue = config.get(section);
        if (typeof configValue === "string") {
            const innerConfig = vscode.workspace.getConfiguration(
                `terminal.integrated.profiles.${section}.${configValue}`
            );
            // Windows uses "PowerShell", so we need to convert to lowercase.
            const name = configValue.trim().toLowerCase();
            const path = innerConfig.get<string>("path");
            const args = innerConfig.get<string[]>("args");
            return this._processShellInfo({ name, path, args }, true);
        }
        return undefined;
    }

    /**
     * Gets all VSCode terminal profiles
     */
    private _getVSCodeProfiles(): ShellInfo[] {
        const profiles: ShellInfo[] = [];
        const section = this._getOSSection();
        const config = vscode.workspace.getConfiguration(`terminal.integrated.profiles.${section}`);
        const profileNames = Object.keys(config);
        for (const profileName of profileNames) {
            const profile = config.get<{ path?: string; args?: string[] }>(profileName);
            if (!profile) {
                continue;
            }
            const name = profileName.toLowerCase();
            const path = profile.path;
            const args = profile.args;
            const processedShell = this._processShellInfo({ name, path, args }, false);
            if (processedShell) {
                profiles.push(processedShell);
            }
        }
        return profiles;
    }

    /**
     * Helper method to get Git Bash information (name, path, args)
     * @param existingPath Optional existing path to use
     * @param existingArgs Optional existing args to use
     * @returns Git Bash shell info or undefined if not found
     */
    private _getGitBashInfo(existingPath?: string, existingArgs?: string[]): ShellInfo | undefined {
        let path = existingPath;
        if (!path) {
            path = this._findGitBashPath();
        }
        if (!path || !fileOrSymlinkToFileExists(path)) {
            return undefined;
        }
        const args = existingArgs ?? ["--login", "-i"];
        return {
            name: "bash",
            path,
            args,
            friendlyName: "Git Bash",
        };
    }

    /**
     * Finds the Git Bash executable path on Windows
     * @returns Path to Git Bash executable or undefined if not found
     */
    private _findGitBashPath() {
        const gitParentPaths = [
            `${process.env["ProgramFiles"]}`,
            `${process.env["ProgramW6432"]}`,
            `${process.env["ProgramFiles(x86)"]}`,
        ];
        for (const parentPath of gitParentPaths) {
            if (!directoryExists(parentPath)) {
                continue;
            }
            const gitPaths = [
                path.join(parentPath, "Git", "bin", "bash.exe"),
                path.join(parentPath, "Git", "usr", "bin", "bash.exe"),
            ];
            for (const gitPath of gitPaths) {
                if (fileOrSymlinkToFileExists(gitPath)) {
                    return gitPath;
                }
            }
        }
        return undefined;
    }

    /**
     * Finds the PowerShell executable path on Windows
     * There are two types of PowerShell on Windows:
     * 1. The legacy Windows PowerShell, which is usually located at
     *    C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe
     * 2. The newer PowerShell, which is usually located at
     *    C:\Program Files\PowerShell\<version>\pwsh.exe
     * We prefer the newer PowerShell with the highest version number,
     * but if it's not found, we fall back to the legacy Windows PowerShell.
     * @returns Path to PowerShell executable or undefined if not found
     */
    private _findPowerShellPath() {
        // Look for paths like C:\Program Files\PowerShell\7\pwsh.exe.
        const programFilePaths = [
            `${process.env["ProgramFiles"]}`,
            `${process.env["ProgramW6432"]}`,
            `${process.env["ProgramFiles(x86)"]}`,
        ];
        let highestVersionNumber: string | undefined = undefined;
        let bestPath: string | undefined = undefined;
        for (const programFilePath of programFilePaths) {
            const powershellPath = path.join(programFilePath, "PowerShell");
            if (!directoryExists(programFilePath) || !directoryExists(powershellPath)) {
                continue;
            }
            for (const [childDir, fileType] of readDirectorySync(powershellPath)) {
                if (fileType === FileType.directory && childDir.match(/^\d+$/)) {
                    const pwshPath = path.join(powershellPath, childDir, "pwsh.exe");
                    if (fileOrSymlinkToFileExists(pwshPath)) {
                        if (!highestVersionNumber || childDir > highestVersionNumber) {
                            highestVersionNumber = childDir;
                            bestPath = pwshPath;
                        }
                    }
                }
            }
        }
        if (bestPath) {
            return bestPath;
        }

        // Look for paths like C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe.
        const possibleArches = ["System32", "SysNative"];
        for (const arch of possibleArches) {
            const windowsPowerShellPath = path.join(
                process.env["windir"] ?? "C:\\Windows",
                arch,
                "WindowsPowerShell",
                "v1.0",
                "powershell.exe"
            );
            if (fileOrSymlinkToFileExists(windowsPowerShellPath)) {
                return windowsPowerShellPath;
            }
        }
        return undefined;
    }

    public get shellName() {
        return TerminalProcessTools._shellInfo.name;
    }

    /**
     * Helper method to truncate output using the AssetManager if available, or fallback to truncateMiddle
     * @param content The content to truncate
     * @param toolUseId The ID to use for storing the untruncated content
     * @param originalCommand Optional original command that generated the content
     * @returns The truncated content
     */
    private async _truncateOutput(
        content: string,
        toolUseId: string,
        originalCommand?: string
    ): Promise<string> {
        if (this._untruncatedContentManager) {
            try {
                const options: TruncateOptions = {
                    maxBytes: this._maxOutputLength,
                    contentType: TruncatedContentType.ToolOutput,
                    toolUseId,
                    maxLinesTerminalProcessOutput: this._maxLinesTerminalProcessOutput,
                    truncationFooterAdditionText: this._truncationFooterAdditionText,
                    toolType: LocalToolType.launchProcess,
                    originalCommand,
                };

                const result = await truncateWithMetadata(
                    content,
                    options,
                    this._untruncatedContentManager,
                    this._enableUntruncatedContentStorage
                );
                return result.truncatedContent;
            } catch (error: any) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                this._logger.debug(`Error using truncateWithMetadata: ${error.message}`);
            }
        }
        // Fall back to truncateMiddle if AssetManager is not available
        return truncateMiddle(content, this._maxOutputLength);
    }

    /**
     * The terminal API in VSCode versions before 1.98.0 is pretty broken: the execution.read
     * method often does not return the actual output.  For details, see the upstream bug
     * https://github.com/microsoft/vscode/issues/237208.
     * We detect this case by checking if we're on a buggy VSCode version and if the output
     * does not contain the escape sequences representing execution completion.  See
     * https://code.visualstudio.com/docs/terminal/shell-integration#_supported-escape-sequences
     * for details on the escape sequences.
     */
    private _isBuggyOutput(text: string) {
        // Check if the current shell has buggy output
        if (this._currentCapability.capability === TerminalCapability.buggyOutput) {
            return true;
        }

        // Otherwise, use the traditional detection method
        return (
            !isVsCodeVersionGte("1.98.0") &&
            !text.includes("\x1b]633;D") &&
            !text.includes("\x1b]133;D")
        );
    }

    private _terminalHasShellIntegration(terminal: vscode.Terminal) {
        // First check if the terminal has shell integration based on capability
        if (!this._isTerminalBasicallySupported()) {
            return false;
        }

        // Then check if this specific terminal has shell integration
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-return
        return (terminal as any).shellIntegration !== undefined;
    }
}

/**
 * A tool that launches a new process.
 */
export class TerminalLaunchProcessTool extends ToolBase<LocalToolType> {
    constructor(
        private readonly _workspaceManager: WorkspaceManager,
        public readonly processTools: TerminalProcessTools
    ) {
        super(LocalToolType.launchProcess, ToolSafety.Check);
    }

    public readonly version = 2;

    public get description(): string {
        return `\
Launch a new process with a shell command. A process can be waiting (\`wait=true\`) or non-waiting (\`wait=false\`).

If \`wait=true\`, launches the process in an interactive terminal, and waits for the process to complete up to
\`max_wait_seconds\` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with \`wait=true\`
while another is running, the tool will return an error.

If \`wait=false\`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use \`wait=true\` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use \`wait=false\` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is ${process.platform}. The shell is '${this.processTools.shellName}'.`;
    }

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            command: {
                type: "string",
                description: "The shell command to execute.",
            },
            wait: {
                type: "boolean",
                description: "Whether to wait for the command to complete.",
            },
            max_wait_seconds: {
                type: "number",
                description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.`,
            },
            cwd: {
                type: "string",
                description:
                    "Required parameter. Absolute path to the working directory for the command.",
            },
        },
        required: ["command", "wait", "max_wait_seconds", "cwd"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(toolInput: Record<string, any>): boolean {
        const allowlist = getShellAllowlist(process.platform, this.processTools.shellName);
        const command = toolInput.command as string;
        return checkShellAllowlist(allowlist, command, this.processTools.shellName);
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        abortSignal: AbortSignal,
        toolUseId: string
    ): Promise<ToolUseResponse> {
        try {
            const wait = toolInput.wait as boolean;
            const requestedMaxWaitSeconds = toolInput.max_wait_seconds as number;
            const cwd =
                (toolInput.cwd as string | undefined) ?? getCwdForTool(this._workspaceManager);
            const useLongRunningTerminal = !!wait;
            const command = toolInput.command as string;

            const pid = await this.processTools.launch(
                command,
                cwd,
                abortSignal,
                useLongRunningTerminal,
                toolUseId
            );
            if (typeof pid === "string") {
                return errorToolResponse(pid);
            }

            if (!wait) {
                return successToolResponse(`Process launched with terminal ID ${pid}`);
            }

            // Get optimal timeout (chooses between predicted and requested)
            const actualTimeout = await this.processTools.getOptimalTimeout(
                command,
                requestedMaxWaitSeconds
            );

            // Wait for process to complete or timeout with tracking
            const result = await this.processTools.waitForProcessWithTracking(
                pid,
                actualTimeout,
                abortSignal
            );
            const afterCwdMessage = getAfterCwdMessage(pid, this.processTools);

            // Check if process is still running based on completion status, not return code
            if (result.status === ProcessStatus.running) {
                return successToolResponse(`\
Command may still be running. You can use read-process to get more output
and kill-process to terminate it if needed.
Terminal ID ${pid}
Output so far:
<output>
${result.output}
</output>
${afterCwdMessage}`);
            }

            return {
                text: `\
The command completed.
Here are the results from executing the command.
Terminal ID ${pid}${result.returnCode !== null ? `\n<return-code>${result.returnCode}</return-code>` : ""}
<output>
${result.output}
</output>
${afterCwdMessage}`,
                isError: result.returnCode !== null && result.returnCode !== 0,
            };
        } catch (e: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            return errorToolResponse(`Failed to launch process: ${e.message ?? ""}`);
        }
    }
}

/**
 * A tool that kills a terminal.
 * Note that this only kills terminals launched with the launch-process tool.
 */
export class TerminalKillProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: TerminalProcessTools) {
        super(LocalToolType.killProcess, ToolSafety.Safe);
    }

    public readonly description: string = "Kill a process by its terminal ID.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to kill.",
            },
        },
        required: ["terminal_id"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const output = await this._processTools.kill(id);
        if (output) {
            if (output.killed) {
                return successToolResponse(
                    `Terminal ${id} killed\n<output>${output.output}</output>`
                );
            } else {
                return successToolResponse(
                    `Terminal ${id} already exited\n<output>${output.output}</output>\n${output.returnCode !== null ? `<return-code>${output.returnCode}</return-code>` : ""}`
                );
            }
        } else {
            return errorToolResponse(`Terminal ${id} not found`);
        }
    }
}

/**
 * A tool that reads output from a terminal.
 * Note that this only reads from terminals launched with the launch-process tool.
 */
export class TerminalReadProcessTool extends ToolBase<LocalToolType> {
    constructor(
        private readonly _processTools: TerminalProcessTools,
        private readonly _workspaceManager: WorkspaceManager
    ) {
        super(LocalToolType.readProcess, ToolSafety.Safe);
    }

    public readonly description: string = `\
Read output from a terminal.

If \`wait=true\` and the process has not yet completed, waits for the terminal to complete up to \`max_wait_seconds\` seconds before returning its output.

If \`wait=false\` or the process has already completed, returns immediately with the current output.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to read from.",
            },
            wait: {
                type: "boolean",
                description: "Whether to wait for the command to complete.",
            },
            max_wait_seconds: {
                type: "number",
                description: `Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed.`,
            },
        },
        required: ["terminal_id", "wait", "max_wait_seconds"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public async call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const wait = toolInput.wait as boolean;
        const maxWaitSeconds = toolInput.max_wait_seconds as number;
        if (wait) {
            await this._processTools.waitForProcess(id, maxWaitSeconds, _abortSignal);
        }

        const output = await this._processTools.hybridReadOutput(id);
        if (!output) {
            return errorToolResponse(`Terminal ${id} not found`);
        }

        const afterCwdMessage = getAfterCwdMessage(id, this._processTools);

        const status =
            this._processTools.getProcessStatus(id) ??
            (output.returnCode !== null ? "completed" : "still running");
        let response = `\
Here is the output from terminal ${id} (status: ${status}):
<output>${output.output}</output>\n`;

        if (output.returnCode !== null) {
            response += `${output.returnCode !== null ? `<return-code>\n${output.returnCode}\n</return-code>` : ""}\n`;
        }
        if (afterCwdMessage) {
            response += afterCwdMessage;
        }

        return successToolResponse(response);
    }
}

/**
 * A tool that writes input to a terminal.
 * Note that this only writes to terminals launched with the launch-process tool.
 */
export class TerminalWriteProcessTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: TerminalProcessTools) {
        super(LocalToolType.writeProcess, ToolSafety.Safe);
    }

    public readonly description: string = "Write input to a terminal.";

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {
            terminal_id: {
                type: "integer",
                description: "Terminal ID to write to.",
            },
            input_text: {
                type: "string",
                description: "Text to write to the process's stdin.",
            },
        },
        required: ["terminal_id", "input_text"],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public call(
        toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const id = toolInput.terminal_id as number;
        const inputText = toolInput.input_text as string;
        if (this._processTools.writeInput(id, inputText)) {
            return Promise.resolve(
                successToolResponse(`Input written to terminal ${id}: \n${inputText}`)
            );
        } else {
            return Promise.resolve(errorToolResponse(`Terminal ${id} not found or write failed`));
        }
    }
}

/**
 * A tool that lists all known terminals and their states.
 * Note that this only lists terminals launched with the launch-process tool.
 */
export class TerminalListProcessesTool extends ToolBase<LocalToolType> {
    constructor(private readonly _processTools: TerminalProcessTools) {
        super(LocalToolType.listProcesses, ToolSafety.Safe);
    }

    public readonly description: string = `List all known terminals created with the ${LocalToolType.launchProcess} tool and their states.`;

    public readonly inputSchemaJson: string = JSON.stringify({
        /* eslint-disable @typescript-eslint/naming-convention */
        type: "object",
        properties: {},
        required: [],
        /* eslint-enable @typescript-eslint/naming-convention */
    });

    public checkToolCallSafe(_toolInput: Record<string, any>): boolean {
        return true;
    }

    public call(
        _toolInput: Record<string, any>,
        _chatHistory: Exchange[],
        _abortSignal: AbortSignal,
        _toolUseId: string
    ): Promise<ToolUseResponse> {
        const processes = this._processTools.listProcesses();
        if (processes.length === 0) {
            return Promise.resolve(successToolResponse("No processes found"));
        }

        const lines = processes.map((proc) => {
            let status = proc.state;
            if (proc.returnCode !== null) {
                status += ` (return code: ${proc.returnCode})`;
            }
            return `Terminal ${proc.id} [${status}]: ${proc.command}`;
        });
        // List the number of running processes to make it clear to the model.
        const runningCount = processes.filter((proc) => proc.state === "running").length;
        if (runningCount === 1) {
            lines.push("\nThere is 1 process still running.");
        } else if (runningCount > 1) {
            lines.push(`\nThere are ${runningCount} processes still running.`);
        }

        return Promise.resolve(
            successToolResponse("Here are all known processes:\n\n" + lines.join("\n"))
        );
    }
}

function getAfterCwdMessage(
    processId: number,
    processTools: TerminalProcessTools
): string | undefined {
    const afterCwd = processTools.getLongRunningTerminalInfo()?.current_working_directory;
    if (!processTools.isInLongRunningTerminal(processId) || !afterCwd) {
        return;
    }
    return `The terminal's current working directory is now \`${afterCwd}\`.\n`;
}

function _stripControlCodes(text: string): string {
    return (
        text
            // Handle OSC sequences that might not start with escape character
            // These can appear in terminal output with shell integration
            // eslint-disable-next-line no-control-regex
            .replace(/(?:\x1b\])?(\d+;[^\x07\x1b]*(?:\x07|\x1b\\))/g, "")
            // Remove ANSI escape sequences
            // eslint-disable-next-line no-control-regex
            .replace(/\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])/g, "")
    );
}

/**
 * Regular expressions for matching common shell prompts and continuation prompts
 */
const _shellPrompts: { [key: string]: RegExp } = {
    powershell: /^\s*(?:PS\s+(?:[^>]*))?\s*>\s*/,
    bash: /^\s*(?:[^@]*@[^:]*:)?(?:[^$]*)?\$\s*/,
    zsh: /^\s*(?:[^@]*@[^:]*:)?(?:[^%]*)?%\s*/,
    fish: /^\s*(?:[^@]*@[^:]*\s)?(?:[^>]*)?>\s*/,
};

/**
 * Gets the appropriate clear command for the shell
 */
function _getClearCommand(shellName: string): string {
    switch (shellName) {
        case "powershell":
            return "Clear-Host";
        case "bash":
        case "zsh":
        case "fish":
        default:
            return "clear";
    }
}

/**
 * Escapes a file path for use in shell commands
 */
function _escapePathForShell(filePath: string, shellName: string): string {
    switch (shellName) {
        case "powershell":
            // PowerShell uses single quotes for literal strings
            return `'${filePath.replace(/'/g, "''")}'`;
        case "bash":
        case "zsh":
        case "fish":
        default:
            // Unix shells - escape special characters
            return `'${filePath.replace(/'/g, "'\"'\"'")}'`;
    }
}

enum TerminalCapability {
    unknown = 1,
    unsupportedAPI = 2,
    shellNotFound = 3,
    noShellIntegration = 4,
    noExecutionEvents = 5,
    buggyExecutionEvents = 6,
    noOutput = 7,
    noisyOutput = 8,
    buggyOutput = 9,
    supported = 10,
}

interface TerminalCapabilityResult {
    capability: TerminalCapability;
    details: string;
}

/**
 * A class that checks if the VSCode terminal has the capabilities we need.
 * It tests for:
 * 1. VSCode version and API support
 * 2. Shell integration availability
 * 3. Terminal execution events
 * 4. Output reliability
 */
class TerminalCapabilityChecker extends DisposableService {
    private _result: TerminalCapabilityResult = {
        capability: TerminalCapability.unknown,
        details: "Check not started",
    };
    private _checkPromise: Promise<TerminalCapabilityResult> | undefined;
    private _logger = getLogger("TerminalCapabilityChecker");
    private _testCommand = "echo 'Terminal capability test'";

    constructor(private readonly _shellInfo: ShellInfo) {
        super();
        void this._startBackgroundCheck();
    }

    /**
     * Checks if the terminal has the capabilities we need.
     * This method can be called multiple times, but will only run the check once
     * unless forceRecheck is true.
     */
    public async checkCapabilities(forceRecheck = false): Promise<TerminalCapabilityResult> {
        // Return cached result if we have one and not forcing a recheck
        if (this._result.capability !== TerminalCapability.unknown && !forceRecheck) {
            return this._result;
        }

        // If a check is already in progress, return that promise
        if (this._checkPromise) {
            return this._checkPromise;
        }

        // Start a new check
        this._checkPromise = this._performCheck();
        try {
            this._result = await this._checkPromise;
            return this._result;
        } finally {
            this._checkPromise = undefined;
        }
    }

    /**
     * Starts the background check for terminal capabilities
     */
    private async _startBackgroundCheck(): Promise<void> {
        try {
            const result = await this.checkCapabilities();
            this._logger.debug(
                `Terminal capability check result: ${this._getCapabilityDescription(result)}`
            );
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error during initial terminal capability check: ${error.message}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingShellCapability,
                conversationId: "",
            });
        }
    }

    /**
     * Gets a human-readable description of the terminal capability
     */
    private _getCapabilityDescription(result: TerminalCapabilityResult = this._result): string {
        return `${TerminalCapability[result.capability]}: ${result.details}`;
    }

    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */
    private async _performCheck(): Promise<TerminalCapabilityResult> {
        const window = vscode.window as any;
        if (!window.onDidEndTerminalShellExecution || !isVsCodeVersionGte("1.93.0")) {
            return {
                capability: TerminalCapability.unsupportedAPI,
                details: `VSCode version ${vscode.version} does not support required terminal APIs`,
            };
        }

        // Check if the shell exists before creating a terminal
        // This prevents VSCode from showing a warning if the shell doesn't exist
        const shellExists = await this._checkShellExists();
        if (!shellExists) {
            return {
                capability: TerminalCapability.shellNotFound,
                details: `Shell ${this._shellInfo.path ?? this._shellInfo.name} does not exist`,
            };
        }

        // Create a hidden terminal for testing
        const terminal = vscode.window.createTerminal({
            name: "Augment Terminal Capability Check",
            hideFromUser: true,
            shellPath: this._shellInfo?.path ?? this._shellInfo.name,
            shellArgs: this._shellInfo?.args,
            env: this._shellInfo?.env,
            isTransient: true,
        });

        try {
            // Wait for shell integration (which will either resolve when the event is received or when it times out)
            const shellIntegrationResult = await this._listenForShellIntegration(terminal);

            // If we got a listener, dispose it
            if (shellIntegrationResult.listener) {
                shellIntegrationResult.listener.dispose();
            }

            if (!shellIntegrationResult.shellIntegrationDetected) {
                return {
                    capability: TerminalCapability.noShellIntegration,
                    details: shellIntegrationResult.message,
                };
            }

            // Run the test command and get the execution and readStream
            const { execution, readStream } = this._executeTestCommand(
                terminal,
                shellIntegrationResult.shellIntegrationDetected
            );

            // Set up the execution event listener
            const executionEventPromise = this._listenForExecutionEvent(
                terminal,
                execution,
                readStream
            );

            // Wait for execution event (which will either resolve when the event is received or when it times out)
            const executionEventResult = await executionEventPromise;

            // If we got a listener, dispose it
            if (executionEventResult.listener) {
                executionEventResult.listener.dispose();
            }

            // Return the capability determined by the execution event listener with appropriate details
            let details: string;
            switch (executionEventResult.capability) {
                case TerminalCapability.buggyExecutionEvents:
                    details = "Terminal receives execution events but for incorrect commands";
                    break;
                case TerminalCapability.noExecutionEvents:
                    details = "No execution events received";
                    break;
                case TerminalCapability.noOutput:
                    details = "Execution event received but no output was captured";
                    break;
                case TerminalCapability.supported:
                    details = "Terminal has all required capabilities with exact output";
                    break;
                case TerminalCapability.noisyOutput:
                    details =
                        "Terminal has all required capabilities but output contains extra content";
                    break;
                case TerminalCapability.buggyOutput:
                    details = "Terminal has all required capabilities but output is incorrect";
                    break;
                default:
                    details = "Unknown terminal capability";
                    break;
            }
            return {
                capability: executionEventResult.capability,
                details,
            };
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Terminal capability check failed: ${error.message}`);
            getAgentSessionEventReporter().reportEvent({
                eventName: AgentSessionEventName.vsCodeTerminalErrorCheckingShellCapability,
                conversationId: "",
            });
            return {
                capability: TerminalCapability.unknown,
                details: `Check failed with error: ${error.message}`,
            };
        } finally {
            // Clean up
            if (terminal) {
                terminal.dispose();
            }
        }
    }

    private _listenForShellIntegration(terminal: vscode.Terminal): Promise<{
        message: string;
        listener?: vscode.Disposable;
        shellIntegrationDetected: boolean;
    }> {
        return new Promise<{
            message: string;
            listener?: vscode.Disposable;
            shellIntegrationDetected: boolean;
        }>((resolve) => {
            if (!terminal) {
                resolve({ message: "Terminal not created", shellIntegrationDetected: false });
                return;
            }

            if (this._terminalHasShellIntegration(terminal)) {
                resolve({
                    message: "Shell integration already available",
                    shellIntegrationDetected: true,
                });
                return;
            }

            // Listen for shell integration to be enabled
            const window = vscode.window as any;
            if (window.onDidChangeTerminalShellIntegration) {
                const listener = window.onDidChangeTerminalShellIntegration((e: any) => {
                    if (e.terminal === terminal && e.shellIntegration) {
                        resolve({
                            message: "Shell integration detected",
                            shellIntegrationDetected: true,
                        });
                    }
                });

                void this._createTimeout("Shell integration", 3000).then((timeoutResult) => {
                    resolve({
                        message: timeoutResult.message,
                        listener,
                        shellIntegrationDetected: false,
                    });
                });
            } else {
                resolve({
                    message: "onDidChangeTerminalShellIntegration API not available",
                    shellIntegrationDetected: false,
                });
            }
        });
    }

    private _listenForExecutionEvent(
        terminal: vscode.Terminal,
        _execution?: any,
        readStream?: AsyncIterable<string>
    ): Promise<{
        listener?: vscode.Disposable;
        capability: TerminalCapability;
    }> {
        return new Promise<{
            listener?: vscode.Disposable;
            capability: TerminalCapability;
        }>((resolve) => {
            const window = vscode.window as any;
            if (!window.onDidEndTerminalShellExecution) {
                resolve({
                    capability: TerminalCapability.noExecutionEvents,
                });
                return;
            }

            const listener = window.onDidEndTerminalShellExecution(async (e: any) => {
                // Check if we received an execution event for our test command
                const isForTestCommand =
                    e.terminal === terminal && e.execution.commandLine.value === this._testCommand;

                // Check if we received an execution event but for a different command
                // This indicates buggy execution events where events are received for other commands
                const isBuggyExecutionEvent =
                    e.terminal === terminal && e.execution.commandLine.value !== this._testCommand;

                if (isForTestCommand) {
                    let capability = TerminalCapability.noOutput; // Default if no output

                    if (readStream) {
                        try {
                            let output = "";
                            for await (const chunk of readStream) {
                                output += chunk;
                            }
                            const cleaned = _stripControlCodes(output.trim());

                            // Determine capability based on output quality
                            if (cleaned && cleaned === "Terminal capability test") {
                                capability = TerminalCapability.supported;
                            } else if (cleaned && cleaned.includes("Terminal capability test")) {
                                capability = TerminalCapability.noisyOutput;
                            } else if (output && output.length > 0) {
                                capability = TerminalCapability.buggyOutput;
                            }
                        } catch (error: any) {
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                            this._logger.debug(`Error reading execution output: ${error.message}`);
                        }
                    }

                    resolve({
                        capability,
                    });
                } else if (isBuggyExecutionEvent) {
                    // We received an execution event but for a different command
                    // This indicates buggy execution events
                    this._logger.debug(
                        `Buggy execution event received for command: ${e.execution.commandLine.value}`
                    );
                    resolve({
                        capability: TerminalCapability.buggyExecutionEvents,
                    });
                }
            });

            void this._createTimeout("Execution event", 2000).then(() => {
                resolve({
                    listener,
                    capability: TerminalCapability.noExecutionEvents,
                });
            });
        });
    }

    private _executeTestCommand(
        terminal: vscode.Terminal,
        shellIntegrationDetected: boolean
    ): { execution?: any; readStream?: AsyncIterable<string> } {
        if (!terminal || !shellIntegrationDetected) {
            return {};
        }

        try {
            // Use shell integration to execute the command if available
            const terminalAny = terminal as any;
            if (terminalAny.shellIntegration) {
                const execution = terminalAny.shellIntegration.executeCommand(this._testCommand);
                const readStream = execution.read();
                return { execution, readStream };
            } else {
                // Fallback to regular sendText
                terminal.sendText(this._testCommand);
                return {};
            }
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error executing test command: ${error.message}`);
            return {};
        }
    }

    private _createTimeout(stage: string, timeoutMs: number): Promise<{ message: string }> {
        return new Promise<{ message: string }>((resolve) => {
            const timeout = setTimeout(() => {
                resolve({ message: `Timeout waiting for ${stage}` });
            }, timeoutMs);
            this.addDisposable(new vscode.Disposable(() => clearTimeout(timeout)));
        });
    }

    private _terminalHasShellIntegration(terminal: vscode.Terminal): boolean {
        return !!(terminal as any).shellIntegration;
    }

    /**
     * Checks if the shell exists by trying to run it.
     * This prevents VSCode from showing a warning if the shell doesn't exist.
     * We're only checking if the shell executable exists and can be started,
     * not if it's fully functional or supports all features we need.
     * @returns true if the shell exists, false otherwise
     */
    private async _checkShellExists(): Promise<boolean> {
        const shellPath = this._shellInfo.path ?? this._shellInfo.name;
        try {
            // For absolute paths, check if the file exists
            if (this._shellInfo.path && isAbsolutePathName(this._shellInfo.path)) {
                return fileOrSymlinkToFileExists(this._shellInfo.path);
            }
            // For shell names without paths, try to run a simple command to check if it exists
            // Use child_process.spawn with shell: false to avoid using the system shell
            // This will throw an error if the command doesn't exist
            return new Promise<boolean>((resolve) => {
                try {
                    const process = spawn(shellPath, [], {
                        shell: false,
                        stdio: "ignore",
                    });
                    process.on("error", () => {
                        // Error event means the shell couldn't be spawned
                        resolve(false);
                    });
                    process.on("exit", () => {
                        // Even if the command returns non-zero, the shell exists
                        resolve(true);
                    });
                    // Set a timeout in case the process hangs.
                    // Some shells might not exit immediately, especially if they're loading config files.
                    // But if we can start the process at all, the shell exists.
                    setTimeout(() => {
                        try {
                            process.kill();
                        } catch (e) {
                            // Ignore errors when killing the process.
                        }
                        resolve(true); // Assume it exists if we could start it but it's hanging.
                    }, 2000); // Increased timeout to give shells more time to start.
                } catch (e) {
                    // Any exception means the shell couldn't be spawned.
                    resolve(false);
                }
            });
        } catch (e) {
            // Any exception means the shell couldn't be checked.
            return false;
        }
    }
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-assignment */

    /**
     * Cleans up resources
     */
    public override dispose(): void {
        super.dispose();
    }
}

// Export for testing
// eslint-disable-next-line @typescript-eslint/naming-convention
export const __test__ = {
    stripControlCodes: _stripControlCodes,
    shellPrompts: _shellPrompts,
    escapePathForShell: _escapePathForShell,
    getClearCommand: _getClearCommand,
    vscodeEventsStrategy: VSCodeEventsStrategy,
    scriptCaptureStrategy: ScriptCaptureStrategy,
    cwdTracker: CwdTracker,
    processStatus: ProcessStatus,
    // Add parseScriptOutput for testing
    parseScriptOutput: (
        rawContent: string,
        actualCommand?: string,
        isCompleted?: boolean
    ): string => {
        // Create a minimal instance to access the private method
        const instance = new ScriptCaptureStrategy();
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        return (instance as any)._parseScriptOutput(rawContent, actualCommand, isCompleted);
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    TerminalLaunchProcessTool,
};
