// This file contains the definitions for CloudIdentityMemberships (aka Google Groups) used to
// grant access to groups of service accounts. These are deployed on a per-project basis. The
// primary use-case is to avoid hitting quotas on the maximum number of principals allowed on IAM
// policies.

local groupDef(namespace, appName, groupName, groupDescription) =
  local groupEmail = '%<EMAIL>' % groupName;

  local objects = [{
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityGroup',
    metadata: {
      name: groupName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: groupDescription,
      parent: 'customers/C02kn0kha',
      groupKey: {
        id: groupEmail,
      },
      // WITH_INITIAL_OWNER means that whoever creates the group (i.e., configconnector) becomes the
      // owner of the group.
      initialGroupConfig: 'WITH_INITIAL_OWNER',
      labels: {
        'cloudidentity.googleapis.com/groups.discussion_forum': '',
      },
    },
  }];

  local addMember(memberEmail, memberNamespace, memberAppName) =
    {
      apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      kind: 'CloudIdentityMembership',
      metadata: {
        name: '%s-%s-membership' % [groupName, memberEmail],
        namespace: memberNamespace,
        labels: {
          app: memberAppName,
        },
      },
      spec: {
        groupRef: {
          name: groupName,
          namespace: namespace,
        },
        preferredMemberKey: {
          id: memberEmail,
        },
        roles: [
          {
            name: 'MEMBER',
          },
        ],
      },
    };

  {
    groupName: groupName,
    groupEmail: groupEmail,
    groupAppName: appName,
    objects: objects,
    addMember: addMember,
  };

{
  metricWriters:: function(env)
    groupDef(
      namespace=(if env == 'DEV' then 'central-dev' else 'central'),
      appName='metric-writers-group',
      groupName='metric-writers',
      groupDescription='Service accounts with roles/monitoring.metricWriter permissions',
    ),
}
