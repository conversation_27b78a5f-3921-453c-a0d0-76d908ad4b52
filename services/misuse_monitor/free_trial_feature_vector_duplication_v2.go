package main

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialFeatureVectorDuplicationV2DryRunFlag = featureflags.NewBoolFlag("free_trial_feature_vector_duplication_v2_dry_run", true)

// extractFieldNamesV2 extracts field names from feature arrays that contain "field_name:field_value" format
func extractFieldNamesV2(features []string) []string {
	var fieldNames []string
	for _, feature := range features {
		if colonIndex := strings.Index(feature, ":"); colonIndex != -1 {
			fieldNames = append(fieldNames, feature[:colonIndex])
		}
	}
	return fieldNames
}

type FreeTrialFeatureVectorDuplicationV2Job struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*FreeTrialFeatureVectorDuplicationV2Job)(nil)

func NewFreeTrialFeatureVectorDuplicationV2Job(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialFeatureVectorDuplicationV2Job, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialFeatureVectorDuplicationV2Job{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-feature-vector-duplicate-v2",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialFeatureVectorDuplicationV2Job) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialFeatureVectorDuplicationV2Job) Run(ctx context.Context) error {
	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting suspect users: %w", err)
	}

	log.Info().Msgf("Total of %d suspect users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error suspending users: %w", err)
	}

	return nil
}

type featureVectorSuspectV2 struct {
	ID          string `bigquery:"opaque_user_id"`
	TenantID    string `bigquery:"tenant_id"`
	MatchReason string `bigquery:"match_reason"`
	// MatchingMachineFields contains field values in "field_name:field_value" format
	// that matched between the suspect user and other users for machine identification
	MatchingMachineFields []string `bigquery:"matching_machine_fields"`
	// MatchingUserFields contains field values in "field_name:field_value" format
	// that matched between the suspect user and other users for user identification
	MatchingUserFields []string `bigquery:"matching_user_fields"`
	// InactiveIDs contains opaque_user_id values of users with inactive subscriptions that matched this suspect
	InactiveIDs []string `bigquery:"inactive_ids"`
	// TrialIDs contains opaque_user_id values of users with trial subscriptions that matched this suspect
	TrialIDs []string `bigquery:"trial_ids"`
	// ProfessionalIDs contains opaque_user_id values of users with professional subscriptions that matched this suspect
	ProfessionalIDs []string `bigquery:"professional_ids"`
	// CommunityIDs contains opaque_user_id values of users with community subscriptions that matched this suspect
	CommunityIDs []string `bigquery:"community_ids"`
	// TeamIDs contains opaque_user_id values of users with team subscriptions that matched this suspect
	TeamIDs []string `bigquery:"team_ids"`
	// EnterpriseIDs contains opaque_user_id values of users with enterprise subscriptions that matched this suspect
	EnterpriseIDs []string `bigquery:"enterprise_ids"`
}

func (m *FreeTrialFeatureVectorDuplicationV2Job) getSuspects(ctx context.Context) ([]*featureVectorSuspectV2, error) {
	// Construct the query.
	query := m.bqClient.Query(`
	DECLARE empty_hash STRING DEFAULT 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855';
	CREATE TEMP FUNCTION arrays_overlap(arr1 ARRAY<STRING>, arr2 ARRAY<STRING>)
	RETURNS BOOL AS (
		(SELECT COUNT(*) FROM UNNEST(arr1) AS val WHERE val IN UNNEST(arr2)) > 0
	);
	CREATE TEMP FUNCTION get_overlapping_features(arr1 ARRAY<STRING>, arr2 ARRAY<STRING>)
	RETURNS ARRAY<STRING> AS (
		(SELECT ARRAY_AGG(val) FROM UNNEST(arr1) AS val WHERE val IN UNNEST(arr2))
	);
	WITH
	-- Get latest feature vector for each user
	latest_feature_vectors AS (
		SELECT
			opaque_user_id,
			feature_vector,
			ROW_NUMBER() OVER (PARTITION BY opaque_user_id ORDER BY time DESC) as rn
		FROM feature_vector_report
	),
	-- Extract all feature values using arrays and unnesting
	feature_extracts AS (
		SELECT
			opaque_user_id,
			category,
			field_name,
			field_value
		FROM latest_feature_vectors
		CROSS JOIN UNNEST([
			STRUCT('machine' as category, 'machine_id' as field_name, JSON_EXTRACT_SCALAR(feature_vector, '$.1') as field_value),
			STRUCT('machine', 'mac_addresses', JSON_EXTRACT_SCALAR(feature_vector, '$.9')),
			STRUCT('machine', 'telemetry_dev_device_id', JSON_EXTRACT_SCALAR(feature_vector, '$.13')),
			STRUCT('machine', 'os_machine_id', JSON_EXTRACT_SCALAR(feature_vector, '$.16')),
			STRUCT('machine', 'user_data_machine_id', JSON_EXTRACT_SCALAR(feature_vector, '$.22')),
			STRUCT('machine', 'baseboard_asset_tag', JSON_EXTRACT_SCALAR(feature_vector, '$.31')),
			STRUCT('machine', 'chassis_asset_tag', JSON_EXTRACT_SCALAR(feature_vector, '$.32')),
			STRUCT('machine', 'memory_module_serials', JSON_EXTRACT_SCALAR(feature_vector, '$.34')),
			STRUCT('machine', 'system_boot_time', JSON_EXTRACT_SCALAR(feature_vector, '$.38')),
			STRUCT('machine', 'hidden_uuid_file_m', JSON_EXTRACT_SCALAR(feature_vector, '$.41')),
			STRUCT('user', 'home_directory_ino', JSON_EXTRACT_SCALAR(feature_vector, '$.17')),
			STRUCT('user', 'git_user_email', JSON_EXTRACT_SCALAR(feature_vector, '$.19')),
			STRUCT('user', 'ssh_public_key', JSON_EXTRACT_SCALAR(feature_vector, '$.20')),
			STRUCT('user', 'user_data_path_ino', JSON_EXTRACT_SCALAR(feature_vector, '$.21')),
			STRUCT('user', 'storage_uri_path', JSON_EXTRACT_SCALAR(feature_vector, '$.23')),
			STRUCT('user', 'system_xdg_folder', JSON_EXTRACT_SCALAR(feature_vector, '$.40')),
			STRUCT('user', 'hidden_uuid_file', JSON_EXTRACT_SCALAR(feature_vector, '$.41')),
			STRUCT('user', 'hostname_username_combo', CONCAT(
				JSON_EXTRACT_SCALAR(feature_vector, '$.6'),
				'@',
				JSON_EXTRACT_SCALAR(feature_vector, '$.8')
			))
		]) AS feature_struct
		WHERE rn = 1
		AND field_value != empty_hash
		AND (field_name != 'hostname_username_combo' OR (
			JSON_EXTRACT_SCALAR(feature_vector, '$.6') != empty_hash
			AND JSON_EXTRACT_SCALAR(feature_vector, '$.8') != empty_hash
		))
	),
	-- Filter out common features using window functions
	all_features AS (
		SELECT
			opaque_user_id,
			category,
			field_name,
			field_value
		FROM (
			SELECT
				opaque_user_id,
				category,
				field_name,
				field_value,
				COUNT(DISTINCT opaque_user_id) OVER (PARTITION BY category, field_name, field_value) as feature_user_count
			FROM feature_extracts
		)
		WHERE feature_user_count <= 200
	),
	-- Aggregate features by category
	feature_data AS (
		SELECT
			opaque_user_id,
			ARRAY_AGG(DISTINCT CASE WHEN category = 'machine' THEN CONCAT(field_name, ':', field_value) END IGNORE NULLS) AS machine_features,
			ARRAY_AGG(DISTINCT CASE WHEN category = 'user' THEN CONCAT(field_name, ':', field_value) END IGNORE NULLS) AS user_features
		FROM all_features
		GROUP BY opaque_user_id
	),
	-- identify users
	user_id AS (
		SELECT
			id AS opaque_user_id,
			tenant_ids[0] AS tenant_id,
			orb_subscription_id AS subscription_id,
			LOWER(email) AS email,
			created_at,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE'
			) as trial_suspended,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_COMMUNITY_ABUSE'
			) as community_suspended,
			EXISTS(
				SELECT 1
				FROM UNNEST(suspensions) AS suspension
				WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL'
			) as disposable_email_suspension,
			suspension_exempt as exempt
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id AS tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
				THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- determine subscription type
	sub AS (
		SELECT
			subscription_id,
			CASE
				WHEN orb_status = 'ORB_STATUS_ACTIVE'
				THEN CASE
					WHEN external_plan_id = 'orb_trial_plan'
					THEN 'TRIAL'
					ELSE 'ACTIVE'
				END
				ELSE 'INACTIVE'
			END AS subscription_category
		FROM subscription
	),
	-- build user profile from all the above
	profile AS (
		SELECT
			feature_data.*,
			user_id.tenant_id,
			user_id.subscription_id,
			user_id.email,
			user_id.created_at,
			user_id.trial_suspended,
			user_id.community_suspended,
			user_id.disposable_email_suspension,
			user_id.exempt,
			CASE
				WHEN sub.subscription_category = 'ACTIVE'
				THEN tier.tier
				WHEN sub.subscription_category = 'INACTIVE'
				THEN CASE
					-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
					WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
					THEN tier.tier
					ELSE 'INACTIVE'
				END
				ELSE sub.subscription_category
			END as category,
			tier.tier,
			sub.subscription_category
		FROM user_id
		JOIN feature_data ON feature_data.opaque_user_id = user_id.opaque_user_id
		JOIN tier ON user_id.tenant_id = tier.tenant_id
		JOIN sub ON user_id.subscription_id = sub.subscription_id
	),
	-- Want to know all detected trial users as suspects.
	suspect AS (
		SELECT
		profile.*
		FROM profile
		WHERE category IN ('TRIAL', 'TEAM_TRIAL', 'COMMUNITY')
		-- Skip suspended and exempt users
		AND NOT (profile.community_suspended AND category = 'COMMUNITY')
		AND NOT (profile.trial_suspended and category IN ('TRIAL', 'TEAM_TRIAL'))
		AND NOT profile.exempt
	),
	-- Find matches using category-based approach with matching field details
	matches AS (
		SELECT
			suspect.opaque_user_id,
			suspect.tenant_id,
			other.category as other_category,
			other.opaque_user_id as other_user_id,
			get_overlapping_features(suspect.machine_features, other.machine_features) AS matching_machine_features,
			get_overlapping_features(suspect.user_features, other.user_features) AS matching_user_features
		FROM suspect
		JOIN profile AS other
		ON suspect.opaque_user_id != other.opaque_user_id
		AND suspect.email != other.email
		AND suspect.created_at > other.created_at
		-- Ignore trials that have been suspended for disposable email
		-- User may use a non-disposable email to create one valid trial.
		AND NOT other.disposable_email_suspension
		WHERE arrays_overlap(suspect.machine_features, other.machine_features)
		AND arrays_overlap(suspect.user_features, other.user_features)
	),
	-- Flatten matching features for aggregation
	flattened_matches AS (
		SELECT
			opaque_user_id,
			tenant_id,
			other_category,
			other_user_id,
			machine_feature,
			user_feature
		FROM matches
		CROSS JOIN UNNEST(matching_machine_features) AS machine_feature
		CROSS JOIN UNNEST(matching_user_features) AS user_feature
	)
	SELECT
		opaque_user_id,
		tenant_id,
		'MACHINE_AND_USER_MATCH' as match_reason,
		ARRAY_AGG(DISTINCT machine_feature) AS matching_machine_fields,
		ARRAY_AGG(DISTINCT user_feature) AS matching_user_fields,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'INACTIVE' THEN other_user_id END IGNORE NULLS) AS inactive_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'TRIAL' THEN other_user_id END IGNORE NULLS) AS trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'PROFESSIONAL' THEN other_user_id END IGNORE NULLS) AS professional_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'COMMUNITY' THEN other_user_id END IGNORE NULLS) AS community_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'TEAM' THEN other_user_id END IGNORE NULLS) AS team_ids,
		ARRAY_AGG(DISTINCT CASE WHEN other_category = 'ENTERPRISE' THEN other_user_id END IGNORE NULLS) AS enterprise_ids
	FROM flattened_matches
	GROUP BY 1, 2
	ORDER BY ARRAY_LENGTH(trial_ids) + ARRAY_LENGTH(inactive_ids) DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var featureVectorSuspects []*featureVectorSuspectV2
	for {
		var row featureVectorSuspectV2
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			featureVectorSuspects = append(featureVectorSuspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(featureVectorSuspects))
	return featureVectorSuspects, nil
}

func (m *FreeTrialFeatureVectorDuplicationV2Job) suspendSuspects(
	ctx context.Context,
	suspects []*featureVectorSuspectV2,
) error {
	dryRun, err := freeTrialFeatureVectorDuplicationV2DryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 0 // Job needs re-evaluating.

	sessionId := requestcontext.NewRandomRequestSessionId()

	// Get wildcard token for suspension operations (no tenant-specific token needed)
	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
		return fmt.Errorf("error getting wildcard token: %w", err)
	}
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

	for _, suspect := range suspects {

		// Suspensions are issued for duplicate inactive, trial, professional and community accounts only.
		// Ignore duplicate teams and enterprise accounts if those are the only duplicates.
		duplicateCount := len(suspect.InactiveIDs) + len(suspect.TrialIDs) + len(suspect.ProfessionalIDs) + len(suspect.CommunityIDs)
		if duplicateCount == 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "ineligible_duplicate", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s in tenant %s, no inactive, trial, professional or community duplicates", suspect.ID, suspect.TenantID)
			continue
		}

		// Suspension NOT issued until at least 2 other accounts are detected
		if duplicateCount < 2 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "trial_count_too_low", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s in tenant %s, only %d trials detected", suspect.ID, suspect.TenantID, duplicateCount)
			continue
		}

		// Check if the user is exempt or already suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, suspect.ID, &suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", suspect.ID, suspect.TenantID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt in tenant %s", suspect.ID, suspect.TenantID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse in tenant %s", suspect.ID, suspect.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		machineFieldNames := extractFieldNamesV2(suspect.MatchingMachineFields)
		userFieldNames := extractFieldNamesV2(suspect.MatchingUserFields)
		evidence := fmt.Sprintf("Duplicate accounts via feature vectors %s (machine: %s, user: %s): inactive: %d trial: %d professional: %d community: %d team: %d enterprise: %d",
			suspect.MatchReason, machineFieldNames, userFieldNames, len(suspect.InactiveIDs), len(suspect.TrialIDs), len(suspect.ProfessionalIDs), len(suspect.CommunityIDs), len(suspect.TeamIDs), len(suspect.EnterpriseIDs))
		log.Info().Msgf("Misuse monitor detected free trial duplication via feature vectors by user %s in tenant %s. %s",
			suspect.ID, suspect.TenantID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, suspect.ID, suspect.TenantID, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", suspect.ID, suspect.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, suspect.ID, suspect.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}
