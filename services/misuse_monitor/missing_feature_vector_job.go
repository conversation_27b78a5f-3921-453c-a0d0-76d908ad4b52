package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

var missingFeatureVectorDryRunFlag = featureflags.NewBoolFlag("missing_feature_vector_dry_run", true)

type MissingFeatureVectorsJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	featureFlagHandle   featureflags.FeatureFlagHandle
}

type missingFeatureVectorSuspect struct {
	ID          string    `bigquery:"opaque_user_id"`
	TenantID    string    `bigquery:"tenant_id"`
	RequestID   string    `bigquery:"request_id"`
	RequestType string    `bigquery:"request_type"`
	UserAgent   string    `bigquery:"user_agent"`
	RequestTime time.Time `bigquery:"request_time"`
	Email       string    `bigquery:"email"`
	Tier        string    `bigquery:"tier"`
	CreatedAt   time.Time `bigquery:"created_at"`
	Category    string    `bigquery:"category"`
}

// Ensure MissingFeatureVectorsJob implements the Job interface
var _ Job = (*MissingFeatureVectorsJob)(nil)

func NewMissingFeatureVectorsJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*MissingFeatureVectorsJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &MissingFeatureVectorsJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "missing-feature-vectors",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *MissingFeatureVectorsJob) Close() {
	m.bqClient.Close()
}

func (m *MissingFeatureVectorsJob) getRecentCount(ctx context.Context) (int, error) {
	query := m.bqClient.Query(`
		select COUNT(*) AS count FROM feature_vector_report where time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return 0, fmt.Errorf("error running query: %w", err)
	}

	var row struct {
		Count int `bigquery:"count"`
	}
	err = it.Next(&row)
	if err == iterator.Done {
		return 0, fmt.Errorf("no results found")
	} else if err != nil {
		log.Error().Msgf("Query results error: %v", err)
		return 0, fmt.Errorf("error parsing query results: %w", err)
	}

	return row.Count, nil
}

func (m *MissingFeatureVectorsJob) getSuspects(ctx context.Context) ([]*missingFeatureVectorSuspect, error) {
	// Construct the query.
	log.Info().Msg("Getting suspects for missing feature vectors job")

	query := m.bqClient.Query(`
	WITH
		-- feature vector information
		recent_feature_vector_reporters AS (
		SELECT
        DISTINCT
          opaque_user_id
        FROM feature_vector_report
        WHERE
          DATE(time) > CURRENT_DATE() - 7 -- optimize query. assume if no report in last 7 days, then not reported..
		),
		recently_active_users AS (
			SELECT
				opaque_user_id,
				tenant_id,
				request_id,
				request_type,
				user_agent,
				time as request_time
			FROM (
				SELECT
					human_request_metadata.opaque_user_id,
					human_request_metadata.tenant_id,
					human_request_metadata.request_id,
					human_request_metadata.time,
					human_request_metadata.request_type,
					human_request_metadata.user_agent,
					ROW_NUMBER() OVER (
						PARTITION BY human_request_metadata.opaque_user_id
						ORDER BY human_request_metadata.time DESC
					) AS rn
				FROM human_request_metadata human_request_metadata
				WHERE
					user_agent LIKE '%vscode%'
					AND request_type IN ('CHAT', 'AGENT_CHAT', 'REMOTE_AGENT_CHAT')
					AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
			) sub
			WHERE rn = 1
		),
		-- identify users
		user_id AS (
			SELECT
				id as opaque_user_id,
				tenant_ids[0] as tenant_id,
				orb_subscription_id as subscription_id,
				created_at,
				LOWER(email) as email,
				EXISTS(
					SELECT 1
					FROM UNNEST(suspensions) AS suspension
					WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE'
				) as trial_suspended,
				EXISTS(
					SELECT 1
					FROM UNNEST(suspensions) AS suspension
					WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_COMMUNITY_ABUSE'
				) as community_suspended,
				EXISTS(
					SELECT 1
					FROM UNNEST(suspensions) AS suspension
					WHERE suspension.suspension_type = 'USER_SUSPENSION_TYPE_DISPOSABLE_EMAIL'
				) as disposable_email_suspension,
				suspension_exempt as exempt
			FROM user
			WHERE ARRAY_LENGTH(tenant_ids) = 1
		),
		-- determine service tier
		tier AS (
			SELECT
				id as tenant_id,
				CASE
					WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
						THEN 'TEAM'
					ELSE tier
				END AS tier
			FROM tenant
		),
		-- determine subscription type
		sub AS (
			SELECT
				subscription_id,
				CASE
					WHEN orb_status = 'ORB_STATUS_ACTIVE'
						THEN CASE
							WHEN external_plan_id = 'orb_trial_plan'
								THEN 'TRIAL'
							ELSE 'ACTIVE'
						END
					ELSE 'INACTIVE'
				END AS subscription_category
			FROM subscription
		),
    tenant_sub_map AS (
      SELECT
        tenant_id,
        orb_subscription_id
      FROM tenant_subscription_mapping
    ),
		-- build user profile from all the above
		profile AS (
			SELECT
				user_id.opaque_user_id,
				user_id.tenant_id,
				user_id.email,
				user_id.created_at,
				user_id.trial_suspended,
				user_id.community_suspended,
				user_id.disposable_email_suspension,
				user_id.exempt,
				CASE
					WHEN sub.subscription_category = 'ACTIVE'
						THEN tier.tier
					WHEN sub.subscription_category = 'INACTIVE'
						THEN CASE
							-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
							WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
								THEN tier.tier
							ELSE 'INACTIVE'
						END
					WHEN sub.subscription_category = 'TRIAL'
						THEN CASE
							WHEN tier.tier = 'TEAM'
								THEN 'TEAM_TRIAL'
							ELSE 'TRIAL'
						END
					ELSE sub.subscription_category
				END as category,
				tier.tier,
				sub.subscription_category
			FROM user_id
			JOIN tier ON user_id.tenant_id = tier.tenant_id
			LEFT JOIN tenant_sub_map ON tier.tier = 'TEAM' AND user_id.tenant_id = tenant_sub_map.tenant_id
			JOIN sub
				ON CASE
				WHEN tier.tier = 'TEAM' THEN tenant_sub_map.orb_subscription_id
				ELSE user_id.subscription_id
				END = sub.subscription_id
		),
		suspects AS (
			SELECT
				opaque_user_id,
				email,
				tier,
				created_at,
				category
			FROM profile
			WHERE category IN ('TRIAL', 'TEAM_TRIAL', 'COMMUNITY')
			-- Skip suspended and exempt free trial users
			AND NOT (profile.community_suspended AND category = 'COMMUNITY')
			AND NOT (profile.trial_suspended and category IN ('TRIAL', 'TEAM_TRIAL'))
			AND NOT profile.exempt
		),
		non_reporters AS (
			SELECT
				recently_active_users.opaque_user_id,
				recently_active_users.tenant_id,
				recently_active_users.request_id,
				recently_active_users.request_type,
				recently_active_users.user_agent,
				recently_active_users.request_time,
				suspects.email,
				suspects.tier,
				suspects.created_at,
				suspects.category,
			FROM
			recently_active_users
			JOIN suspects USING (opaque_user_id)
				LEFT JOIN recent_feature_vector_reporters USING (opaque_user_id)
			WHERE
			recent_feature_vector_reporters.opaque_user_id IS NULL
        ),
		last_7days_usage_count AS (
			SELECT
				opaque_user_id,
				COUNT(*) AS usage_count
			FROM human_request_metadata
			JOIN non_reporters USING (opaque_user_id)
			WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
				AND human_request_metadata.request_type IN ('CHAT', 'AGENT_CHAT', 'REMOTE_AGENT_CHAT')
			GROUP BY 1
		)
		SELECT
			opaque_user_id,
			tenant_id,
			request_id,
			request_type,
			user_agent,
			request_time,
			email,
			tier,
			created_at,
			category,
		FROM
			non_reporters
		JOIN last_7days_usage_count USING (opaque_user_id)
		WHERE last_7days_usage_count.usage_count >= 10
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var missingFeatureVectorItems []*missingFeatureVectorSuspect
	for {
		var row missingFeatureVectorSuspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			missingFeatureVectorItems = append(missingFeatureVectorItems, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialConversationDuplicates to ban", len(missingFeatureVectorItems))
	return missingFeatureVectorItems, nil
}

func (m *MissingFeatureVectorsJob) suspendSuspects(ctx context.Context, suspects []*missingFeatureVectorSuspect) error {
	dryRun, err := missingFeatureVectorDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	} else {
		log.Info().Msg("*** WET RUN! Suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	communitySuspensionsEnabled := communitySuspensionsEnabled(m.featureFlagHandle)

	// slice to limit
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()

	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
		return fmt.Errorf("error getting wildcard token: %w", err)
	}

	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

	for _, suspect := range suspects {

		// Check if the user is exempt or already suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, suspect.ID, &suspect.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", suspect.ID, suspect.TenantID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt in tenant %s", suspect.ID, suspect.TenantID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse in tenant %s", suspect.ID, suspect.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		// Community accounts are not suspended if the feature flag is not enabled.
		if suspect.Category == "COMMUNITY" && !communitySuspensionsEnabled {
			log.Info().Msgf("User %s is in community tier, skipping", suspect.ID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "community_suspensions_disabled", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		// suspend

		evidence := fmt.Sprintf("Account did not send identifying data. Last request: %s %s %s", suspect.RequestTime, suspect.RequestType, suspect.UserAgent)
		log.Info().Msgf("Misuse monitor detected missing feature vectors by user %s in tenant %s. %s",
			suspect.ID, suspect.TenantID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionType := auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE
			if suspect.Category == "COMMUNITY" {
				suspensionType = auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_COMMUNITY_ABUSE
			}
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, suspect.ID, suspect.TenantID, suspensionType, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", suspect.ID, suspect.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, suspect.ID, suspect.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()

	}
	return nil
}

func (m *MissingFeatureVectorsJob) Run(ctx context.Context) error {
	recentCount, err := m.getRecentCount(ctx)
	if err != nil {
		return fmt.Errorf("error getting recent count: %w", err)
	}

	if recentCount < 50000 {
		log.Info().Msgf("Too few recent feature vectors %d, skipping", recentCount)
		return nil
	}

	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting suspect users: %w", err)
	}

	log.Info().Msgf("Total of %d suspect users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error suspending users: %w", err)
	}
	return nil
}
