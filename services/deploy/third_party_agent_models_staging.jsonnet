/**
  Defines third-party agent-chat model configurations used across deployment files
 */

local templates = import 'services/deploy/third_party_agent_models_template.jsonnet';

{
  add_suffix: {
    'claude-sonnet-4-0-200k-v9-global': templates.agent_model_agnostic + {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-sonnet-4@20250514',
      prompt_formatter_name: 'agent-binks-claude-v9',
      gcp_region: 'global',
      max_output_tokens: 1024 * 16,  // 16k for response
    },
    'grok4-200k-v7': templates.agent_model_agnostic + {
      client_type: 'xai',
      model_name: 'grok-4-0709',
      prompt_formatter_name: 'agent-binks-grok4-v7',
      gcp_region: 'us-central1',
    },
    'grok4-200k-v8': templates.agent_model_agnostic + {
      client_type: 'xai',
      model_name: 'grok-4-0709',
      prompt_formatter_name: 'agent-binks-grok4-v8',
      gcp_region: 'us-central1',
    },
    'gpt5-reasoning-200k-v7': templates.agent_model_agnostic + {
      client_type: 'openai_direct',
      model_name: 'nectarine-alpha-new-reasoning-effort-2025-07-25',
      prompt_formatter_name: 'agent-binks-gpt5-v7',
      gcp_region: 'us-central1',
      temperature: 1,
      max_output_tokens: 1024 * 12,
      reasoning_effort: 'low',
    },
    'gpt5-reasoning-medium-200k-v7': templates.agent_model_agnostic + {
      client_type: 'openai_direct',
      model_name: 'nectarine-alpha-new-reasoning-effort-2025-07-25',
      prompt_formatter_name: 'agent-binks-gpt5-v7',
      gcp_region: 'us-central1',
      temperature: 1,
      max_output_tokens: 1024 * 12,
      reasoning_effort: 'medium',
    },
    'gpt5-reasoning-high-200k-v7': templates.agent_model_agnostic + {
      client_type: 'openai_direct',
      model_name: 'nectarine-alpha-new-reasoning-effort-2025-07-25',
      prompt_formatter_name: 'agent-binks-gpt5-v7',
      gcp_region: 'us-central1',
      temperature: 1,
      max_output_tokens: 1024 * 12,
      reasoning_effort: 'high',
    },
    'gpt5-reasoning-medium-0724-200k-v7': templates.agent_model_agnostic + {
      client_type: 'openai_direct',
      model_name: 'nectarine-alpha-2025-07-24',
      prompt_formatter_name: 'agent-binks-gpt5-v7',
      gcp_region: 'us-central1',
      temperature: 1,
      max_output_tokens: 1024 * 12,
      reasoning_effort: 'medium',
    },
    'kimi-k2-200k-v7': templates.agent_model_agnostic + {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/kimi-k2-instruct#zhuoran-e5c41f/csv0ir7p',
      prompt_formatter_name: 'agent-binks-kimi-k2-v7',
      max_output_tokens: 1024 * 16,  // 16k for response
      token_apportionment+: {
        max_prompt_len: 1024 * 128 - (1024 * 16),  // 128k context minus 16k for output
        tool_results_len: 1024 * 60,  // ~60% of remaining context for tools
        token_budget_to_trigger_truncation: 1024 * 60,
      },
    },
    'qwen3-coder-200k-v7': templates.agent_model_agnostic + {
      client_type: 'fireworks',
      model_name: 'accounts/fireworks/models/qwen3-coder-480b-a35b-instruct',
      prompt_formatter_name: 'agent-binks-qwen3-coder-v7',
      temperature: 0.7,
      top_p: 0.8,
      top_k: 20,
      repetition_penalty: 1.05,
      max_output_tokens: 1024 * 16,  // 16k for response, Note that Qwen recommends 64k for response, but we're starting with 16k
      token_apportionment+: {
        max_prompt_len: 1024 * 128 - (1024 * 16),  // 128k context minus 16k for output
        tool_results_len: 1024 * 60,  // ~60% of remaining context for tools
        token_budget_to_trigger_truncation: 1024 * 60,
      },
    },
  },
  'gpt5-low-200k-v7': templates.agent_model_agnostic + {
    client_type: 'openai_direct',
    model_name: 'gpt-5-2025-08-07',
    prompt_formatter_name: 'agent-binks-gpt5-v7',
    gcp_region: 'us-central1',
    temperature: 1,
    max_output_tokens: 1024 * 12,
    reasoning_effort: 'low',
  },
  'gpt5-med-200k-v7': templates.agent_model_agnostic + {
    client_type: 'openai_direct',
    model_name: 'gpt-5-2025-08-07',
    prompt_formatter_name: 'agent-binks-gpt5-v7',
    gcp_region: 'us-central1',
    temperature: 1,
    max_output_tokens: 1024 * 12,
    reasoning_effort: 'medium',
  },
  'gpt5-high-200k-v7': templates.agent_model_agnostic + {
    client_type: 'openai_direct',
    model_name: 'gpt-5-2025-08-07',
    prompt_formatter_name: 'agent-binks-gpt5-v7',
    gcp_region: 'us-central1',
    temperature: 1,
    max_output_tokens: 1024 * 12,
    reasoning_effort: 'high',
  },
  no_suffix: {},
}
