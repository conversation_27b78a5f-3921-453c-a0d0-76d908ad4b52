local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local cmkLib = import 'deploy/common/cmk_lib.jsonnet';
local eng = import 'deploy/common/eng.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local cloudIdentityGroupsLib = import 'deploy/gcp/cloud-identity-groups-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(cloud)
  if cloud == 'GCP_US_CENTRAL1_PROD' then
    local bucketName = 'augment-data';
    local accessUsers = [user.username for user in eng if user.gcp_access != null];
    local bucket = {
      apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      kind: 'StorageBucket',
      metadata: {
        annotations: {
          'cnrm.cloud.google.com/force-destroy': 'false',
        },
        name: bucketName,
        namespace: 'central',
      },
      spec: {
        versioning: { enabled: true },
        uniformBucketLevelAccess: true,
        lifecycleRule: [],
      },
    };
    local bucketAccess =
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'data-bucket-access-policy',
          namespace: 'devtools',
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: 'augment-data',
          },
          bindings: [
            // dont' hand out update access as that can be used to
            // corrupt existing checkpoints making them unloadable.
            {
              role: 'roles/storage.objectCreator',
              members: std.map(function(u)
                {
                  member: 'user:%<EMAIL>' % u,
                }, accessUsers),
            },
            {
              role: 'roles/storage.objectViewer',
              members: std.map(function(u)
                {
                  member: 'user:%<EMAIL>' % u,
                }, accessUsers),
            },
          ],
        },
      };

    local prodCmkAccessServiceAccount = gcpLib.createIAMServiceAccountOnly(
      app='prod-cmk-access',
      env='PROD',
      cloud=cloud,
      namespace='central',
      iamServiceAccountName=cmkLib.getCmkServiceAccountName('PROD')
    );

    local stagingCmkAccessServiceAccount = gcpLib.createIAMServiceAccountOnly(
      app='staging-cmk-access',
      env='STAGING',
      cloud=cloud,
      namespace='central-staging',
      iamServiceAccountName=cmkLib.getCmkServiceAccountName('STAGING')
    );

    local metricWritersGroup = cloudIdentityGroupsLib.metricWriters('PROD');
    local metricWriterGrant = gcpLib.grantAccess(
      name='metric-writers-grant',
      env='PROD',
      namespace='central',
      appName=metricWritersGroup.groupAppName,
      resourceRef={
        kind: 'Project',
        external: cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/monitoring.metricWriter',
          members: [{ member: 'group:%s' % metricWritersGroup.groupEmail }],
        },
      ],
    );

    lib.flatten([
      bucket,
      bucketAccess,
      prodCmkAccessServiceAccount.objects,
      stagingCmkAccessServiceAccount.objects,
      metricWritersGroup.objects,
      metricWriterGrant,
    ])

  else if cloud == 'GCP_US_CENTRAL1_DEV' then
    local bucketName = 'augment-data-dev';
    local bucket = {
      apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      kind: 'StorageBucket',
      metadata: {
        annotations: {
          'cnrm.cloud.google.com/force-destroy': 'false',
        },
        name: bucketName,
        namespace: 'devtools',
      },
      spec: {
        versioning: { enabled: true },
        uniformBucketLevelAccess: true,
        lifecycleRule: [],
      },
    };
    local accessUsers = [user.username for user in eng if user.gcp_access != null];
    local bucketAccess =
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'data-bucket-access-policy',
          namespace: 'devtools',
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucketName,
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: std.map(function(u)
                {
                  member: 'user:%<EMAIL>' % u,
                }, accessUsers),
            },
          ],
        },
      };

    local devCmkAccessServiceAccount = gcpLib.createIAMServiceAccountOnly(
      app='dev-cmk-access',
      env='DEV',
      cloud=cloud,
      namespace='central-dev',
      iamServiceAccountName=cmkLib.getCmkServiceAccountName('DEV')
    );

    local metricWritersGroup = cloudIdentityGroupsLib.metricWriters('DEV');
    local metricWriterGrant = gcpLib.grantAccess(
      name='metric-writers-grant',
      env='DEV',
      namespace='central-dev',
      appName=metricWritersGroup.groupAppName,
      resourceRef={
        kind: 'Project',
        external: cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/monitoring.metricWriter',
          members: [{ member: 'group:%s' % metricWritersGroup.groupEmail }],
        },
      ],
    );

    lib.flatten([
      bucket,
      bucketAccess,
      devCmkAccessServiceAccount.objects,
      metricWritersGroup.objects,
      metricWriterGrant,
    ])

  else []
