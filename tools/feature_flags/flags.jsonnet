// Feature Flags
//
// see https://www.notion.so/Dynamic-Feature-Flags-112bba10175a803fb0ceef0ba2ea986c?pvs=4
//
// Dev/Test
// Set `extraFakeFeatureFlags` flags in dev-defaults.jsonnet namespace config or your namespace config.
//

local agents_wave2_latest = [
  // Agents Wave 2 03/04/2025 - alpha survey respondents
  {
    user_id_hmac: [
      'f5945017b5eaf42b95747ab4d1c0979b173c726d1f0e318ed79fa88bdcde0975',
      '96d6b44dfad6d8671bc9633c9309f0918203a49b7b528e3a3b317b5c0c63cfd9',
      '2ff753b0d2a74c3c1ed88512fb816032000d65022bbf0d9a17e7d056ffadb7ba',
      '689e9a80cbec35ea4bf51788ef71ed5d967303f355c8fc2b60256c1faad7f07e',
      '9b06acfd9f4d0f9fc6861db3aee986f73d4f2aee4c088ffd691a1f9eec039278',
      'eb4d50c811e125cdceab1d442bce9ea84b921f707edc1d196d369d24f71647a6',
      'd9f7c9afc07f7e28c25b79c0ce14864bbfd272352c8810c4c3dbdb66f2234683',
      'f2ea37719a3ed3ba7f5e2812ea01f7c2a9e21331f5893f7f30188baa4998bfc9',
      'ed8b995aaeb6826afed6711dee15437b96a9d2e59b73a01922ecf08905278366',
      'b796e0dae62f846025ed92b31822a52dd41146e433463a75cc0b84f1b3acf2b1',
      'eb324c63b1a0478411e3843b0fe01f020903a6c9a75af1d24a3186be027ebb68',
      'a06b42b186b79f1dc83d13621956fe6316c588ccd6c3dd7a29a6e59487782de7',
      '02e660b7c0a94a4ee1d9e62c742707ebf7770c7a0a20bdd736b92dd7d8ac4a7e',
      'cbc965b2dae88e190a420ffea77fde7e81a7c1ed5797461f0bb5ab5d32eb54e9',
      '2087230ea760088d218808f0a0ab0a44bf005d98be0957aee35e41f3207058ff',
      '69e51793108c992b955c37985044fa85c0d9437c542b4c61d44fb8f7ec0ac0de',
      'fdd77de1e06af0902b67e3a3fb2938cdd3f9468bedd17c062fe82919eb250f6e',
      '03d3c69b20cb144eb4a22bbc4be6cea7c8d3662676167890f59280ab11c4ab88',
      '69b9934f6fb3e976b380dffad9f6043168b32d6145761a75e3495c0a15402535',
      'd5b5bdeeaed35704ec5c9986da0fbfe1304623f4415ad6b207c3d8b11b53c896',
      'b44123cf0b6d84aa13fd87efd3a8ecfdee5127507e95e0118d1d81dc33e5adfa',
      'a0af28e1d486d6aa12fccbb2afdc2ed9d82a7f201f67d0c2230992a1acf79966',
      '1097453508262e9c92d836d6445ca0b9cda4f76e1e1efbd06989e61bcfa619ba',
      '64f3b93b18f9f8879ff50212c219e55f826bfc0045f7ab56de0708c7f206bfc0',
      '154ff7f7ff20f57827d231790f5a790e0cbc935fe9932ed847674b1344c6794a',
      '358f649af621babc6f448c738b8d862b068987d355ee55c419d9d46a21a5a190',
      '546bf98956e058e96567e3e876904ce24aaae1e76d46bab60ca3d2817fb75be6',
      '161496ca5c3ed164f5ce0ee4e7c1a057ca72d19a42a202a6b3a0cf98eafad9b3',
    ],
    return_value: error 'Must override return_value',
  },
  {
    user_uuid: [
      '949bbece-5e2b-4446-9f9e-e66c3072a5d8',
      'ba7683ba-5405-4fd1-83b5-1c5928084f90',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave2_stable = [
  // Agents Wave 2 03/04/2025
  {
    tenant_name: [
      'lemonade',
      'gofundme',
      'silanano',
      'samaya',
    ],
    return_value: error 'Must override return_value',
  },
  // Per-user release for UXR study
  {
    user_uuid: [
      'aefe7a6d-7e82-49fa-8460-483915a0ee7c',
      'c21cac9a-356e-484f-a7c9-ff200b940be4',
      '793802e4-21b1-48c9-aac6-8bbc4410f757',
      'bc886046-fa20-4aa7-8901-d2139b36aa7b',
      'ee0089fc-47cd-4fa0-b9ad-426416748e90',
      'dae0eb29-9c8f-4dfd-9f3e-0d06996f73cd',
      'd21e5bba-00fe-4e4e-89e1-43ce9eaa3d72',
      '15c5b99a-9a8a-4940-867d-1725cfbb4b07',
      'aee3549a-8e9a-4fee-8c59-85ace6a24010',
      'fd4eeb80-caa6-48f2-8498-22fbb526c069',
      'f278b9e5-7a7f-47c4-9247-c82b413f289d',
      '1af7b81a-5c8c-4827-b985-ed3918c5b1a9',
      '9b6d6185-da34-45c8-a370-72a6951cf32e',
      'e657790c-e013-4833-a323-496dea9fc45e',
      '5a574608-a781-4123-82d6-f1f0d7a725da',
      '934dd871-0776-48d1-98b8-fea6c9ef65a5',
      'f218c87b-e934-4e8d-875a-2cb409d8a711',
      '137576a4-fcda-4e46-a919-4ae14d851b2e',
      '74a8fb90-2924-4647-8bec-9c444b62f078',
      'd7256a21-8dc9-4123-91ec-de4b83f8e381',
      '97d5a790-37c9-4960-be7f-af2893e5db71',
      '3e572482-dc8f-4c21-acc4-da34ae0579f5',
      '46c779dc-8e80-45ac-99a5-1675330101eb',
      '1400cee7-27c0-4937-bd5a-d3c65b5ae077',
      '2e6b0bc0-45f9-4a2e-8500-f55a35b10856',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave3_latest = [
  // Agents Wave 3 - 03/12/2025
  {
    // Discord survey respondents
    user_uuid: [
      '473404a3-8980-4ba2-aef4-b778708c7252',
      'e6761413-9d06-40f0-84d9-3c5c3546711d',
      'b91cf981-c7ff-4094-ac24-97c31b55bdd7',
      'f82846a7-9032-4679-9c2f-419346896ab3',
      '338f6962-7dfa-4768-a0c9-e8ffd4ff2aa1',
      '71e37722-10a8-433f-a0ee-dccfa6d7157b',
      '6d467092-679b-4205-8320-6a65bdcb6b0e',
      '451f9782-e35b-4229-8f48-b5606af372c9',
      '93b749a5-909d-487a-be14-29fa89d537f0',
      'a8639802-194f-4d64-9b28-9e962012990a',
      '8f356c27-790c-4c5f-8802-7f242c3e0b09',
      'fa2efe3f-76a5-4efa-b955-11589ec9d25c',
      '29bdfef2-b7d8-4541-8b16-b87969f708ba',
      'cc96af3d-fa9d-4867-9815-ffe8df28b904',
      '7865ea62-0cc4-42bb-b0d5-73d739af3fa4',
      '4a9d08be-836a-48b8-bd67-7d4f2db43549',
      '145c5c1e-4ede-4b81-bf3c-0b64f090a7fe',
      '23db6deb-912a-490c-89d0-9cb876ae0a93',
      'a09aa7a8-d8a7-4917-93f8-99dc0eba9ac3',
      '68287948-73d9-4f16-af34-561c6fd59ddb',
      '261136af-2ce1-4128-9c0e-6b0622bf8653',
      '02205316-bf32-48b7-a4e3-3fdf8dc07839',
      '2cb7e878-a3c4-4322-88b9-a1bb9c7c9dc8',
      '47c55acc-b01a-433a-b629-a29660f7265c',
      '985be049-e7a8-43a2-923c-76317b553077',
      'ba7322f9-b1e1-458f-bf04-c6c8b910b124',
      'a18b0d13-7b69-4160-a6cd-c25e16e4d41f',
      '749ef78b-dff4-46a5-98d2-6d8814ed5442',
      '1d7250dd-69bd-4eae-8b78-eb26f93e512a',
      '5b1fdaeb-64b0-4b86-b5d0-e5f5ad76d3f6',
      '91d474f4-eb2b-477e-b677-f9963c1061db',
      'fbdc52ba-bedf-4664-838f-e5c5acd0dafc',
      '5c826800-ff6f-4b5e-b944-a2a1ba6522ab',
      'bcefc6c3-ccd2-48fc-98d5-95ef9873cdae',
      'fe03e19f-4d43-4b86-80f1-a3b956ce5ddc',
      'd252fcda-d100-4eab-b0e4-061e3a189402',
      '5851d37e-d95f-402f-927b-bb5a18500bf6',
      '26e54dc5-e459-44db-95f1-8ed0f7dcc067',
      '2ba1de16-d0d4-41a5-97c0-476951451f46',
      'eeb88865-5c54-4e7c-a8c5-3dde3f306b62',
      '039ad530-fca8-4ff4-8218-1dc01d0c0bef',
      'ee05c9a2-38a0-48d1-826e-7a8bc1cc2f9f',
      '8cb3b90d-2f93-43b2-8e35-fb3e5d836437',
      '95282f8b-e684-440c-9f2c-6359aca3e130',
      'b5d9b2f8-72d0-40a4-bb1c-21faa5f772c0',
      'ceb06bc9-6a73-489a-8769-0ed892ec3da3',
      '1203f51d-1e24-4094-b67d-a29da7e695c5',
      '7747e9af-13a3-45f8-a7ec-8670e9e69c1c',
      '42746878-2be4-401b-be86-63f90811c77b',
      '812ac506-6f2f-4f6b-b39f-98f31de8bd5a',
      '7790a3ec-bfce-4b31-acfc-89046eb3160b',
      '7e2e48d2-3bd4-4852-86ac-2ecda0ed6e0c',
      '3c753a74-18b8-4cb1-9e38-21e5799dd9b9',
      '07616a04-c51d-47a6-abc2-f960c7eb31b7',
      '198016fc-ad58-4d4f-a2be-3247c28dda18',
      '2c35e823-ccaf-44e4-b478-31df3865547b',
      '93bf7295-1fa3-4f12-a6c2-a835d639c63e',
      '1eb302ce-fc5a-44fe-9aad-e03386932bc9',
      '9f4dfd6a-8ab4-4dff-930e-60ec062c7d7f',
      '7d0c9407-97b3-4bb9-9bcc-5939753542b6',
      'fc749434-7538-42fc-a93f-740cfb8dc3fc',
      '87ab1461-2219-4997-87ba-50c48f2fc647',
      'b5f6922e-b2a8-4683-bb7d-6ffbaaaced61',
      '1c56cdd6-1108-46e2-b820-6bc745885be0',
      '0358c2b2-7a26-44bf-bce4-4b0187a9eda9',
      'ded9d259-3690-4827-9d9d-e2d7d89227ab',
      '3f1dc451-fb9f-4366-9b7c-728c2c5671b7',
      'b634fff0-1302-41a5-99f6-439b2f1d3def',
      'f7232894-d418-467b-ad4c-812ddfc5850c',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave3_stable = [
  // Agents Wave 3 - 03/12/2025
  {
    tenant_name: [
      'intercom',
      'kognitos',
      'rippling',
      'lyrahealth',
      'newfront',
      'purestorage',
      'sifive',
      'sigma',
      'docker',
      'peek',
      'spoton',
      'reveart',
      'montecarlodata',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // Individuals from enterprise tenants not listed above
    user_uuid: [
      'a0bc19f2-de12-45fd-8a2d-986b4c27fb07',
      '6a54216c-b046-4b63-b450-420a4f5d9a51',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave4_latest = [
  // Agents Wave 4 - 03/18/2025
  {
    // Fraction of Discovery namespaces
    namespace: [
      'd6',
      'd8',
      'd14',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // Discord survey respondents, "influencers"
    user_uuid: [
      'b0dbb29a-8fa2-4dbc-99a4-7244874b9e54',
      'f492c2a9-932d-410b-96f4-4aadf95af595',
      'c07957e1-34eb-4988-a1ea-877331cbc47c',
      '433bd312-b5dd-48fa-9fab-22010de6129d',
      '5b0732ec-3565-4447-84d6-447ca4ed13e6',
      '03f22743-ae89-4f87-8bcf-d1a42b228c01',
      '426bcb86-941a-4d49-b21a-c2f13df441cc',
      'd1135721-5a85-410a-a50d-af9ac0d5db9d',
      'e4fd87bd-a116-4f7b-9bb7-b58eb3f6ab41',
      '5c3d327c-dbdc-45ac-8e29-be1acee321f2',
      '627e6bb7-a528-41cd-91fd-59282a5f0eee',
      'fd615575-2064-4451-93f2-61b7f766714b',
      'dc931be0-bc94-482a-b507-cdb99af5cd63',
      '0fb4b415-ccd5-40b2-9ad0-0e6774080950',
      '415c6aa3-b3f6-48db-ab54-395d2c19e6da',
      '35a3cd94-ce0f-497e-955d-b3f4f02c9208',
      '410f85f2-a012-4eec-9848-113ca0be08c6',
      'bb688832-03b0-419e-aa8e-dbe2c0e06b2f',
      '40accfae-ef9e-4ea0-9617-b1dfae99615d',
      '8393c37c-e3ce-4ba1-8e7e-d3870b6ba41f',
      '2514ff6e-aa7a-4856-8be2-4b917003c36b',
      '4a179971-9f37-4e31-821e-40fccfb1648a',
      'e6b33bef-346a-47f5-aaa6-979dafae664d',
      'a3c776ef-9d88-4385-9729-68e77ec1fd47',
      '3e208280-0a69-4fbb-9472-f376406dc29b',
      'e30c2e43-7ba8-441d-a5e1-6b0f8cf2eee4',
      'f0425203-4861-4ffe-81f2-53d298df5b0a',
      '8cd779cf-6b43-4c0e-a285-fbc45027e28e',
      '5cffe052-ff15-4cb3-aeb3-4c584f5631ab',
      'ed22e5f6-1a31-43d1-87e8-66fe44823832',
      '0ea14393-6e73-478a-95dc-a79e35a1a707',
      'ee44a967-7480-420e-a407-78823c533070',
      '174cb68a-53e7-4338-a316-2bc851ae2210',
      'c91e8f3d-4fa9-4789-89f4-c889a2948483',
      'f4eadcca-d0ad-4889-bdf6-66b154e03a30',
      '34ea6533-7f32-4383-95fb-aa2a25681b83',
      '66894ba5-d65f-406f-9d1f-0c62b6f03ca1',
      '8566479f-1ae4-447f-acf5-d7c855dd9979',
      '2f099e9d-1074-4cf4-8f75-8dee513a2f85',
      '683c9617-929e-421c-b294-b322ec4b57b8',
      '452a9339-2e61-46e6-95fa-c237a328d713',
      'c0916bb4-2217-4d6c-bade-5ec1ad8b8fe5',
      '8ef5925e-64aa-425c-b046-ae257ed81620',
      // Extending the list 03/21/2025 to bring in more jetbrains users for
      // that wave
      '6afcde04-9062-4962-97ad-969bd52f5b26',
      '21ed18dd-f621-46b7-9dcd-8e28d422e2b1',
      'ba7322f9-b1e1-458f-bf04-c6c8b910b124',
      'db44ab77-bcf3-4223-911a-9eba94789ac7',
      '93b749a5-909d-487a-be14-29fa89d537f0',
      '10266d40-ad47-423e-b195-2ff0ab2c41ea',
      'a6a7eea7-d735-4bda-8997-d917f4c1b704',
      '87ab1461-2219-4997-87ba-50c48f2fc647',
      '1203f51d-1e24-4094-b67d-a29da7e695c5',
      '3f1dc451-fb9f-4366-9b7c-728c2c5671b7',
      '194d0839-8ba8-42bb-b126-ac092bae0794',
      '198016fc-ad58-4d4f-a2be-3247c28dda18',
      'd0133efe-e915-4a8c-9e2e-27e5beb5087d',
      '0cd5e68a-7c74-40bd-81fe-9016abb3cd49',
      '03f50c38-1e21-4ec8-9c7f-9454fd81e59d',
      '93a12483-9044-4e2c-98cc-e455c38b282f',
      'e8f14080-4d10-4659-913a-52eff28d9460',
      '5ad4d7a1-4e18-4bf5-8e0e-1d29ba4fe468',
      'c1193993-2b3c-49dc-88cd-2a8607d5b95f',
      '2137863d-e068-4e68-bb1a-781cea6d3e4b',
      '67697b93-1348-4a36-9ba7-612bac4333b0',
      'eeb88865-5c54-4e7c-a8c5-3dde3f306b62',
      'ceb06bc9-6a73-489a-8769-0ed892ec3da3',
      'ee05c9a2-38a0-48d1-826e-7a8bc1cc2f9f',
      '261136af-2ce1-4128-9c0e-6b0622bf8653',
      '256660fd-7fc6-431b-a668-47ff98d47040',
      '7747e9af-13a3-45f8-a7ec-8670e9e69c1c',
      '71e37722-10a8-433f-a0ee-dccfa6d7157b',
      'b1bc7b3e-703e-4852-837c-8293ba0a17f0',
      '32fa9400-9ec0-4512-b98f-965f5fbab9a4',
      'a3639d1b-9f96-435f-a5de-0b9cabb534e3',
      '5c826800-ff6f-4b5e-b944-a2a1ba6522ab',
      '6d467092-679b-4205-8320-6a65bdcb6b0e',
      '6a19b5e2-f620-4b8d-8e15-7406ee4ee032',
      '092e678b-4ca8-4d54-8801-8d1d737e5856',
      'e7200aac-d918-4ff5-ba68-c5396483e0ae',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave4_stable = [
  // Agents Wave 4 - 03/18/2025
  {
    tenant_name: [
      'afresh',
      'maxar',
      'filevine',
      'tecton',
      'gojitsu',
      'cisco',
      'floqast',
      'digikey',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_rubrik_allowlist = [
  // Rubrik Allowlist - 05/21/2025
  {
    user_uuid: [
      '95c24b04-2721-4c67-af50-acf0ea3af438',
      'a42588e9-ac6d-452b-a8cd-a746788b7571',
      'c0bbadd4-a843-42f1-90e1-096a0476fd08',
      '15f0a4ed-c338-4321-8c2d-713f046ecfb1',
      '5393b8ab-3faf-436a-9055-d6602c85cd65',
      '2d40c058-d8ac-43b1-8fe3-45f97ef41b7a',
      '6caac782-3cfb-489e-a116-963d0f81901b',
      '883fb7ff-54d6-4ed9-84b0-460f6f7a78bc',
      'd888f816-0333-4af8-8a9e-a3ec27184699',
      '38118a4f-7bdc-4139-878f-f634d4dfe71a',
      '14ba1a44-5391-4af6-8c0d-2e476f19d643',
      '874b0108-0dd7-4f25-9e11-f6227eee45ae',
      'd05f3dcb-3160-45ac-8619-07bf43f5b07d',
      // 2nd wave
      '9152972f-ac65-42b2-80fd-a9c412b2daf3',
      'fcc75145-b1dc-4206-a2aa-f15d8b722476',
      '6b99e39d-2e43-49ae-b53d-b73bdbdc15b0',
      '1f2e37b9-5576-470f-8c2b-0028dc556543',
      '6c3d7f3f-79ee-48ee-b823-8163becfcae4',
      '65a3f65b-a27b-4830-8b90-fb0d14373c09',
      'ceae083a-13ca-4692-bbc8-839b2ebd31ef',
      '11202063-3a59-434a-9158-9a0d63f43d2b',
      'cbafc55d-2366-4578-ad07-9cefd40ec9db',
      '79e42b97-6b19-4f82-9293-534e51f74403',
      '28054859-f77f-40e9-a461-bc71107bf5ac',
      '1b1f2239-0d86-404f-a7dd-dacf0b844c4d',
      '409ba2f6-e017-46fe-bdb0-865a08315589',
      'c478b406-9920-45f2-8022-753de59fac5e',
      'c2d31b4f-b00b-4e9f-b488-ac4620fba2b5',
      '642340a0-b54a-4f69-b2cb-f8498dda15ea',
      'acdfa712-cbb0-47c2-828d-732e871eaf57',
      '28c7e51f-6a2a-4971-a7a1-aaf7bae3ff83',
      '38b20808-665f-4549-9c8f-d5fb9202cc8c',
      '17c06c71-5375-4b1d-84ee-8c98cc11b059',
      'f1aad7fb-5cdf-4523-a7a5-2b201ea7bedb',
      'a22ac128-d16f-4ea6-a962-e02623ed8e79',
      '303f33eb-88be-4a01-864d-ad18aecd448b',
      '0628bdfa-2724-4a98-a1db-624aa43c4c1e',
      'de965e36-5dd4-4de5-9fe5-f9df29642388',
      'ca1f9c24-1602-4acc-b566-f938a855ebee',
      '734938e1-32c8-4896-b285-8880254330af',
      '06a53e31-84c8-42b1-bd93-b849aa56c5e2',
      '32a921b6-08c4-4dbf-b0cb-0912f8a8a185',
      '2b43be61-37b4-404a-b8aa-02d9014b47e2',
      '7f660b67-14d3-404a-8ac3-02e9282fc4d1',
      '87ae217c-5183-486f-80c9-8804ea87aa6d',
      // 3rd wave
      '8171b374-ae9a-4662-97e5-9716b8c44740',
      '434eb0da-e499-4a92-b35b-cf6fdcb091ae',
      'fa8baf96-8d4f-4631-a958-565495074250',
      '86ab7a56-95cb-4e09-bfe6-e368526abf05',
      '88a2b1bd-0821-43c2-af27-3efaadfa5f25',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_intellij = [
  // IntelliJ Wave - 03/21/2025
  {
    tenant_name: [
      'tecton',
      'filevine',
      'gojitsu',
      'egnyte',
      'webflow',
      'turvo',
      'gopigment',
      'purestorage',
      'collectors',
      'onelineage',
      'junipersquare',
      'aitutor-turing',
    ],
    return_value: error 'Must override return_value',
  },
];
// Until we can actually ban users, use this group to throttle their requests.
// Users are identified by queries added in #22313.
local chat_banlist = [
  {
    user_uuid: [
      '12763cb7-e22f-4caa-b462-2d20bc7d2a06',
      'e8e05ee5-89f6-42ff-8203-c488ef224f8e',
      'f585c242-d2e2-45a6-833d-5a4bc9cba79a',
      '1f55615e-c648-44ec-b9c5-e0d5b0a2b6a2',
      '4121ae59-c8cb-4598-a30b-28b85a838fa4',
      '0c36947b-3710-471e-87d1-6ac38e4bab5c',
      '8549867b-0e5b-46c3-bdda-c5f586e40d90',
      'fcdfca65-356a-47aa-863c-5c4d1ac53a93',
      '0d60b71a-0b9e-4897-8dc7-d8c0483126aa',
      'db92b6f9-66b6-4711-bdda-4d47d5a21b1d',
      'a615bc73-6e3c-4fa7-85c5-b262b0581b7a',
      '4a634ca6-e77b-4935-8d73-435e7feacbfc',
      '9691f885-6f10-4e45-a7f2-a7df9cf21d1b',
      '0ecfd906-01bd-4031-a67d-27e4b5453e3a',
      '9a6ffde9-0d7f-4c0b-a487-7e953b40e915',
      '4ce41b21-87f9-4f1a-a96f-c44b751f505c',
      'ccbc8f12-13e6-4cfb-aa50-ed10cf68be64',
      '595c59c4-b53b-4975-a512-7d3a56d4aeb5',
      '787f067e-cac5-4824-979b-f30c9de30cd1',
      'b3b5291c-f791-43cf-8fa8-421feac47e9c',
      '3fd7218a-48e4-40d6-a4ee-7c1f02a830b3',
      'f7dfbafc-2864-43fb-bea4-69ac1ea3c638',
      '955aa537-cb78-482e-a170-31c7ed81e40a',
      'a608bd9d-1d8e-4242-bcd7-3de2b046e81a',
      'b23bb4a4-9072-4d36-a8ea-b8c8d4b1f959',
      'c3abd85b-86c8-4f58-b80f-5b29be632280',
      'ecd930de-02f1-4949-b7ab-829ed348e1e0',
      '0a10bb0b-5c30-44de-8941-5a6c8373eda4',
      '2f1e7a36-2523-4bce-9ebd-aca9fd659f2a',
      'cb5c9f6b-655c-44b9-8bf3-2d2b5e6f3ba7',
      '5cc8aa8e-c57e-4b4a-ae18-9091344f4b05',
      '6f158176-fb25-476f-a2e1-2af70f410d56',
      'c4505de8-4156-4592-90d0-476a68b575d8',
    ],
    return_value: error 'Must override return_value',
  },
];

// Enterprise tenants with remote agents enabled. This list contains all enterprise tenants that do
// not have CMK enabled and are not in the EU, as those are not supported yet.
local remote_agents_enterprise_tenants = [
  'accelos',
  'advisor360',
  'aitutor-mercor',
  'aitutor-turing',
  'ampsortation',
  'augmentdemo',
  'chaidiscovery',
  'coalitioninc',
  'collective',
  'collectors',
  'docana',
  'eikon',
  'ef',
  'enfabrica',
  'flume',
  'gladly',
  'gofundme',
  'humaninterest',
  'lemonade',
  'lettuce',
  'lmnt',
  'lyrahealth',
  'montecarlodata',
  'newfront',
  'nucleus',
  'observeinc',
  'onebuild',
  'onelineage',
  'paystone',
  'pocketlaw',
  'pollyex',
  'purestorage',
  'realtor',
  'replicahq',
  'reveart',
  'roboto',
  'samaya',
  'schoolstatus',
  'sifive',
  'sigma',
  'tangotango',
  'webflow',
  'jotai',
  'accenture',
  'accountants',
  'aeroflowinc',
  'afresh',
  'alida',
  'amazon',
  'amplitude',
  'antler',
  'atommap',
  'atmosphere',
  'athena',
  'augmentmarket',
  'avalara',
  'banyaninfra',
  'betterup',
  'bigcartel',
  'bigcommerce',
  'blend360',
  'boeing',
  'bonfy',
  'brainvoy',
  'brex',
  'campus',
  'capsule',
  'carta',
  'chegg',
  'cisco',
  'clearme',
  'clutch',
  'codem',
  'comarch',
  'coolplanet',
  'cribl',
  'datastax',
  'ddn',
  'detroitsoftware',
  'discord',
  'divelement',
  'docker',
  'doctoranywhere',
  'dotdashmdp',
  'drata',
  'dreamgames',
  'dropbox',
  'ebrd',
  'eightfold',
  'epam',
  'etsy',
  'faire',
  'fashiondigital',
  'farmart',
  'ferryhealth',
  'filevine',
  'flyr',
  'fmad',
  'fmglobal',
  'fnal',
  'fourkites',
  'gatik',
  'gilead',
  'glassnode',
  'globality',
  'google',
  'grafana',
  'grammarly',
  'greenberry',
  'groq',
  'gusto',
  'hawaiianair',
  'hey',
  'holidayextras',
  'infogain',
  'itradenetwork',
  'itsmorse',
  'jackson',
  'jaggaer',
  'jpmchase',
  'juniper',
  'kindermorgan',
  'king',
  'kla-tencor',
  'knak',
  'knowbe4',
  'kyro',
  'lattice',
  'leadtech',
  'lentra',
  'lepton',
  'lodgistics',
  'logos',
  'makenotion',
  'medallia',
  'metacoregames',
  'momentummedia',
  'monzo',
  'netdocuments',
  'nextgeneration',
  'nextracker',
  'niceforyou',
  'novozymes',
  'onwish',
  'osf',
  'outreach',
  'oxide',
  'paloaltonetworks',
  'peek',
  'perfios',
  'plaid',
  'plutis',
  'proximity',
  'public-us',
  'questlabs',
  'quorum',
  'ramp',
  'redis',
  'ritchiebros',
  'ro',
  'roomsync',
  'sequencefilm',
  'shipwire',
  'shv',
  'silanano',
  'slalom',
  'smartfren',
  'snowflake',
  'sparelabs',
  'specstory',
  'spoton',
  'stratadecision',
  'sysco',
  'taskrabbit',
  'teamwork',
  'techmahindra',
  'tecton',
  'tencent',
  'thebrowser',
  'timescale',
  'toasttab',
  'trustpilot',
  'twilio',
  'uncountable',
  'upwork',
  'usestyle',
  'vercel',
  'veson',
  'viasat',
  'vista',
  'walleyecapital',
  'wearenotch',
  'wehaa',
  'wiz',
  'workato',
  'wwt',
  'x',
  'zapier',
  'zenbusiness',
  'zoyya',
  'zup',
  'augment-test-1',
  'regrello1',
  'abrigo',
  'acadialps',
  'admarketplace',
  'adobe',
  'affirm',
  'agilize',
  'agoda',
  'aim',
  'aimclear',
  'airslate',
  'airwallex',
  'aisleplanner',
  'aivoicespace',
  'alarm',
  'alteryx',
  'altscore',
  'anduril',
  'answerrocket',
  'arista',
  'ascendum',
  'ashleyfurniture',
  'asite',
  'avaropoint',
  'axonius',
  'bailliegifford',
  'banedigital',
  'bedrock',
  'bhphoto',
  'bidgely',
  'bigid',
  'bizdras',
  'blend-ed',
  'bridgefront',
  'bullish',
  'cadstrom',
  'catonetworks',
  'cello',
  'cigna',
  'citrine',
  'clarifyventures',
  'clay',
  'cloudera',
  'comprehensive',
  'compstak',
  'condenast',
  'convai',
  'coralogix',
  'crowdfarming',
  'crypto',
  'dialpad',
  'digikey',
  'displayr',
  'docyt',
  'doordash',
  'dremio',
  'dxc',
  'egnyte',
  'eibach',
  'elea',
  'elevancehealth',
  'emids',
  'empower',
  'familysearch',
  'fieldai',
  'figma',
  'flockfreight',
  'fortissolutionsg',
  'foundationdevice',
  'fpcomplete',
  'friedkin',
  'futurefertility',
  'gameopedia',
  'garnerhealth',
  'gemini',
  'gloat',
  'gmicloud',
  'gojitsu',
  'gomomentus',
  'graydi',
  'groww',
  'hardy',
  'hasura',
  'hebbia',
  'hillspire',
  'hist',
  'hockeystack',
  'ibaset',
  'incode',
  'indykite',
  'infinitereality',
  'infotech',
  'instacart',
  'intercom',
  'intrigma',
  'jarustech',
  'jumptrading',
  'junipersquare',
  'keyplay',
  'knorket',
  'kognitos',
  'labcorp',
  'lambda',
  'learnosity',
  'lendable',
  'lendingtree',
  'luminarycloud',
  'luxurypresence',
  'matrixcare',
  'mercury',
  'moneygram',
  'mongodb',
  'mookti',
  'moonactive',
  'multiplier',
  'namadgi',
  'narvar',
  'netflix',
  'netinspect',
  'netlify',
  'neuroforge',
  'nexthop',
  'ninetwothree',
  'ninthwave',
  'nomihealth',
  'novaprime',
  'nsightcare',
  'nue',
  'numenta',
  'octonova',
  'optient',
  'optimalcomplianc',
  'ostro',
  'overit',
  'packsize',
  'pax8',
  'paychex',
  'pepperdash',
  'personio',
  'pingidentity',
  'place',
  'planyear',
  'platformqcouk',
  'plazz',
  'practice',
  'pres',
  'produce8',
  'producthunt',
  'psum',
  'pylon',
  'quantumsoft',
  'rain',
  'relativityspace',
  'rewind',
  'rippling',
  'root',
  'runllama',
  'shinyrobot',
  'showpad',
  'silverkeytech',
  'simplicate',
  'simplifi',
  'siteboss',
  'skf',
  'smartsensesoluti',
  'smyrna',
  'sorenson',
  'statsig',
  'stingray',
  'stravu',
  'surgepays',
  'sweep',
  'swizzleai',
  'sysdig',
  'techempower',
  'teiko',
  'tekion-us',
  'tetrascience',
  'thoughtworks',
  'thumbtack',
  'tide',
  'toolsgroup',
  'toolstation',
  'triumpharcade',
  'uhligcom',
  'ujet',
  'ukr',
  'unigroup',
  'upgrade',
  'veefin',
  'vizientinc',
  'volarium',
  'walmart',
  'wealthsimple',
  'welltower',
  'withintrinsic',
  'workday',
  'x5wgroup',
  'xai',
  'xoriant',
  'ylopo',
  'youscience',
  'ysecurity',
  'zemosolabs',
  'zerity',
];

// Make sure we don't enable remote agents for rubrik
assert !std.any(
  std.map(
    function(tenant) std.member(tenant, 'rubrik'),
    remote_agents_enterprise_tenants,
  ),
) : 'rubrik must NOT be enabled for remote agents';

local self_serve_namespaces = [
  'i0',
  'i1',
  'd0',
  'd1',
  'd2',
  'd3',
  'd4',
  'd5',
  'd6',
  'd7',
  'd8',
  'd9',
  'd10',
  'd11',
  'd12',
  'd13',
  'd14',
  'd15',
  'd16',
  'd17',
  'd18',
  'd19',
  'd20',
];

local cli_enterprise_tenant_allowlist = [
  // Partners
  'shv',  // Sutter hill, investor and partner
  'snowflake',  // Exploring CLI partnership

  // Customers
];

local cli_enterprise_tenant_trials = [
  'advisor360',  // Trial start 07-31-2025
  'afresh',  // Trial start 08-06-2025
  'avid',  // Trial start 07-31-2025
  'bonfy',  // Trial start 07-31-2025
  'callminer',  // Trial start 08-04-2025
  'cato',  // Trial start 2025-08-05
  'crypto',  // Trial start 07-31-2025
  'ddn',  // Trial start 07-31-2025
  'doordash',  // Trial start 07-31-2025
  'drata',  // Trial start 07-31-2025
  'dremio',  // Trial start 07-31-2025
  'empower',  // Trial start 07-31-2025
  'garnerhealth',  // Trial start 07-31-2025
  'hillspire',  // Trial start 08-02-2025
  'infinitereality',  // Trial start 07-31-2025
  'intercom',  // Trial start 07-31-2025
  'lambda',  // Trial start 07-31-2025
  'lemonade',  // Trial start 07-31-2025
  'lendable',  // Trial start 07-31-2025
  'matrixcare',  // Trial start 07-31-2025
  'maxar',  // Trial start 07-31-2025
  'moneygram',  // Trial start 07-31-2025
  'netdocuments',  // Trial start 07-31-2025
  'nexthop',  // Trial start 07-31-2025
  'observeinc',  // Trial start 07-31-2025
  'packsize',  // Trial start 07-31-2025
  'personio',  // Trial start 07-31-2025
  'purestorage',  // Trial start 07-31-2025
  'realtor',  // Trial start 08-04-2025
  'redis',  // Trial start 07-31-2025
  'ringcentral',  // Trial start 07-31-2025
  'rubrik-cmk',  // Trial start 07-31-2025
  'silanano',  // Trial start 07-31-2025
  'tetrascience',  // Trial start 08-04-2025
  'wayfair',  // Trial start 07-31-2025
  'webflow',  // Trial start 07-31-2025
  'workday',  // Trial start 07-31-2025
  'ysecurity-cmk',  // Trial start 07-31-2025
];

local cli_self_serve_users = [
  // Partners for integration
  '2285f942-fa25-4ef4-8dc0-85fd18393ecf',  // Snowflake tester account - potential CLI partner
  '0566868f-be79-40cd-8a97-8bd0f9aa41ca',  // zed.dev - exploring integrating CLI with their IDE

  // Trial Users
  'abf45da0-6468-4f52-a06e-4e24be8e6851',  // hud.io - Trial start 07-31-2025

  // Friends and promoters

  // Waitlist beta users
];


local apply_return_value(rules, value) =
  std.map(function(rule) rule + { return_value: value }, rules);

// Shared memory prompts to reduce duplication
local shared_memory_prompts = {
  injection_prompt: |||
    Here are the memories already saved:
    ```
    {currentMemories}
    ```
    Here is the new memory to remember:
    ```
    - {newMemory}
    ```
    Incorporate the new memory into the current memories, by adding/removing/modifying memories as needed.
    If new memory is already present in current memories, just return current memories as is.

    Memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)
    Example format of updated memories:
    ```
    # Group 1
    - Memory 1
    - Memory 2

    # Group 2
    - Memory 3
    - Memory 4

    ...
    ```

    Write ONLY full updated memories and NOTHING else. Start your response with "```" and end it with "```". Don't do ANY preamble or postamble.
  |||,
  compression_prompt: |||
    Here are the full memories assembled through all interactions of user with coding agent:
    ```
    {memories}
    ```
    You task is to summarize and merge related or redundant memories to retain the most informative and distinct ones.
    Result should be ~{compressionTarget} lines long.

    Prioritize preserving information that is:
    - Relevant to codebase, policies, preferred technologies or patterns
    - Will be useful in long-term
    - Describes user, user knowledge, user preferences or long-term information about user (like name/email)
    {recentMemoriesSubprompt}

    Updated memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)
    Example format of updated memories:
    ```
    # Group 1
    - Memory 1
    - Memory 2

    # Group 2
    - Memory 3
    - Memory 4

    ...
    ```

    Write ONLY full updated memories and NOTHING else. Start your response with "```" and end it with "```". Don't do ANY preamble or postamble.
  |||,
  recent_memories_subprompt: |||
    - Also here are the most recent memories that should be preserved (do not create "recent memories" group, though):
    ```
    {recentMemories}
    ```
  |||,
  classify_and_distill_prompt: |||
    ###
    # ENTER MESSAGE ANALYSIS MODE
    # IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING
    # YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF
    # YOU RETURN ONLY JSON
    # ###

    Here is the next message from the user:
    ```
    {message}
    ```
    Your task is to detect if the next message contains some information worth remembering in long-term.
    Information is worth remembering if in the next message user gives some new information, asks to do something differently or describes user preferences.
    Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!
    Also, if user hints to how/where tests should be written, it is also worth remembering.
    If knowledge is overly specific to the current task, then it is NOT worth remembering.
    If user reports some task specific bug, it is NOT worth remembering.

    Exceptions (do not remember such information):
    - If user asks not to use some existing tools

    Return JSON with three keys (in provided order): "explanation" (str), "worthRemembering" (bool) and "content" (str).
    "explanation" should be short (1 sentence) text that describes why the information is worth remembering or not.
    "content" should be short (1 sentence) text that describes the information worth remembering.
    If "worthRemembering" is false, then "content" should be empty.

    Write ONLY JSON and no other text (start response with "{"). All planning/reasoning/etc should be put into "explanation". Don't use any tools for it.
    Example: {"explanation": "some explanation", "worthRemembering": true or false, "content": "memory content"}

  |||,
  classify_and_distill_success_prompt: |||
    ###
    # ENTER MESSAGE ANALYSIS MODE
    # IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING
    # YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF
    # YOU RETURN ONLY JSON
    # ###
    Here is the next message from the user:
    ```
    {message}
    ```
    Your task is to detect if the next message contains some information worth remembering in long-term.
    Information is worth remembering if in the next message user gives some new information, asks to do something differently or describes user preferences.
    Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!
    Also, if user hints to how/where tests should be written, it is also worth remembering.
    If knowledge is overly specific to the current task, then it is NOT worth remembering.
    If user reports some task specific bug, it is NOT worth remembering.
    Exceptions (do not remember such information):
    - If user asks not to use some existing tools
    IMPORTANT: Only save memories for exchanges that resulted in SUCCESS. Look for these success indicators:
    - Code was committed to a repository (mentions of "commit", "committed", "git commit", "pushed")
    - Changes were merged into a pull request ("PR", "pull request", "merged")
    - User expressed gratitude: "thanks", "thank you", "great", "perfect", "awesome", "excellent"
    - User indicated satisfaction: "works", "working", "solved", "fixed", "done"
    - User confirmed the solution was helpful
    Return JSON with three keys (in provided order): "explanation" (str), "worthRemembering" (bool) and "content" (str).
    "explanation" should be short (1 sentence) text that describes why the information is worth remembering or not.
    "content" should be short (1 sentence) text that describes the information worth remembering.
    If "worthRemembering" is false, then "content" should be empty.
    Write ONLY JSON and no other text (start response with "{"). All planning/reasoning/etc should be put into "explanation". Don't use any tools for it.
    Example: {"explanation": "some explanation", "worthRemembering": true or false, "content": "memory content"}
  |||,
  classify_and_distill_scope_prompt: |||
    ###
    # ENTER MESSAGE ANALYSIS MODE
    # IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING
    # YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF
    # YOU RETURN ONLY JSON
    # ###
    Here is the next message from the user:
    ```
    {message}
    ```
    Your task is to detect if the next message contains some information worth remembering in long-term.
    Information is worth remembering if in the next message user gives some new information, asks to do something differently or describes user preferences.
    Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!
    Also, if user hints to how/where tests should be written, it is also worth remembering.
    If knowledge is overly specific to the current task, then it is NOT worth remembering.
    If user reports some task specific bug, it is NOT worth remembering.
    Exceptions (do not remember such information):
    - If user asks not to use some existing tools
    For each memory, identify its SCOPE:
    - CODEBASE: Specific file paths, modules, or components mentioned (e.g., "/src/api/auth.ts", "authentication module")
    - TASKS: Related units of work, features, or tasks (e.g., "user authentication", "API integration", "database migration")
    - CATEGORIES: Broader technical areas (e.g., "TypeScript", "React", "testing", "performance", "security", "code style")
    Return JSON with three keys (in provided order): "explanation" (str), "worthRemembering" (bool) and "content" (str).
    "explanation" should be short (1 sentence) text that describes why the information is worth remembering or not.
    "content" should be short (1 sentence) text that describes the information worth remembering.
    If "worthRemembering" is false, then "content" should be empty.
    Write ONLY JSON and no other text (start response with "{"). All planning/reasoning/etc should be put into "explanation". Don't use any tools for it.
    Example: {"explanation": "some explanation", "worthRemembering": true or false, "content": "memory content"}
    Example content format:
    "[SCOPE: /src/api/auth.ts, /src/utils/validation.ts; user authentication, input validation; TypeScript, security] User prefers using Zod for validation in authentication flows"
  |||,
  language_localization_prompt: |||
    This tree represents all folders in the codebase that contain {programmingLanguage} files:
    ```
    {languageTree}
    ```

    Please analyze this tree and summarize major places where {programmingLanguage} code is used.

    At the end write a final response as a one-line, comma-separated list of paths to these major places.
    Enclose this list in a pair of XML tags called <locations></locations>.

    Example response:
    <locations>src/project1,/project2/,/project3/tools</locations>
  |||,
  detect_languages_prompt: |||
    Here is the list of most common file extensions in the codebase:
    ```
    {fileExtensionsList}
    ```
    Can you please list most important programming languages used in the project?

    Exclude any languages that are declerative such as JSON, YAML, ProtoBuf, etc.
    Exclude any languages that have INSIGNIFICANT AMOUNT of code in the codebase.

    Combine any variants of a language into a single entry. For example, treat "ts" and "tsx" as "ts".
    Also combine language of the same family as a single entry. For example, "js/ts", "c/cpp".

    Respond a JSON that maps language name to the list of file extensions present in the codebase.
    Example response: {"python": ["py"], "js/ts": ["js", "ts", "tsx"], "java": ["java"]}

    Respond ONLY with the JSON. Do not include any preceeding or succeeding text.
    If no programming languages are used, respond with an empty JSON.
  |||,
  orientation_compression_prompt: |||
    Here are results of codebase analysis:
    ```
    {assembledKnowledge}
    ```

    Summarize it into a file `AGENT.md` containing important information about the codebase.
    Especially include:
    - build and test commands
    - tests organization

    The file you create will be given to agentic coding agents (such as yourself) that operate in this repository. Make it no longer than 60 lines.
    Enclose the file in a pair of XML tags called <agent_md></agent_md>.
  |||,
  orientation_build_test_query: |||
    You are inside a codebase. Content of root folder (output of "ls ."):
    ```
    {rootFolderContent}
    ```

    Here is a list of major locations where {language} code is used:
    ```
    {locationList}
    ```

    Your task is to figure out build and test commands (especially for running a single test) for {language} part of the codebase.
    Also figure out general tests organization.

    Make sure to check all of these locations when figuring out the build and test commands.
    Note that different part of the codebase might use different build and test tools, so you need to check all of them.

    Return your response using the "complete" tool.
  |||,
};

// Shared memory configuration parameters
local shared_memory_config = {
  enable_memories_tracing: true,
  upper_bound_size: 80,
  compression_target: 60,
  remember_tool_model_name: 'gemini-2-flash-001-simple-port',
  num_recent_memories_to_keep: 5,
  orientation_max_languages: 3,
  orientation_concurrency_level: 2,
  orientation_model_name: 'claude-sonnet-3-7-simple-c4-p2-chat',
};

{
  model: {
    sync: true,
    description: '',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'qweldenv4-14b',
          },
          {
            return_value: 'qweldenv3-3-14b',
          },
        ],
      },
    },
  },
  edit_model: {
    sync: true,
    description: '',
    envs: {
      production: {
        rules: [
          {
            return_value: 'droid-187-33B-FP8-seth-edit',
          },
        ],
      },
    },
  },
  next_edit_model: {
    sync: true,
    description: 'Model for next-edit requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'raven-edit-v6-15b',
          },
        ],
      },
    },
  },
  instruction_model: {
    sync: true,
    description: 'Model for instruction requests',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'claude-instruction-v4-edit',
          },
          {
            return_value: 'claude-instruction-v3-edit',
          },
        ],
      },
    },
  },
  instruction_fallback_models: {
    sync: true,
    description: 'Comma separated list of fallback Models for instruction requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-instruction-v2-edit',
          },
        ],
      },
    },
  },
  smart_paste_model: {
    sync: true,
    description: 'Model for smart-paste requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'forger-v2-qwen-14b-q-32k-edit',
          },
        ],
      },
    },
  },
  smartpaste_force_fuzzy_search: {
    sync: true,
    description: 'Whether or not to use fuzzy search for Forger-based smart paste',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'i0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  apply_instruction_streaming_in_handler: {
    sync: true,
    description: 'If true, enable instruction streaming in the instruction handler',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  connectivity_test_flag: {
    sync: false,
    description: '',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  chat_model: {
    sync: true,
    description: 'The default chat model to use for LEGACY front ends. All newer clients should use chat_raw_output_model instead.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-v18-c4-p2-chat',
          },
        ],
      },
    },
  },
  chat_raw_output_model: {
    sync: true,
    description: 'Default model for chat requests with raw output feature enabled',
    envs: {
      production: {
        rules: [
          {
            // Keep AI Tutors on the same version as Dogfood, unless due to deployment
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 'claude-sonnet-v18-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-v18-c4-p2-chat',
          },
        ],
      },
    },
  },
  chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for chat requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-v17-balanced-c4-p2-chat',
          },
        ],
      },
    },
  },
  chat_raw_output_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for chat requests with raw output feature enabled',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 'claude-sonnet-v17-balanced-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-v17-balanced-c4-p2-chat',
          },
        ],
      },
    },
  },
  additional_chat_models: {
    sync: true,
    description: 'Add secondary models to chat - PUBLIC - use sha256 hash of model name',
    envs: {
      production: {
        rules: [
          {
            return_value: '{}',
          },
        ],
      },
    },
  },
  agent_chat_model: {
    sync: true,
    description: 'Default chat model for agent-mode chat requests',
    envs: {
      production: {
        rules: [
          {
            // Keep AI Tutors on the same version as Dogfood, unless due to deployment issues
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 'gpt5-reasoning-medium-200k-v7-c4-p2-agent',
          },
          {
            return_value: 'claude-sonnet-4-0-200k-v8-c4-p2-agent',
          },
        ],
      },
    },
  },
  agent_chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for agent-mode chat requests',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '',
          },
          {
            // Fallout from AU-11783: we may get a resource exhausted error from
            // chat host without communicating with anthropic. So "fallback" to
            // the same model as the primary model.
            return_value: 'claude-sonnet-4-0-200k-v8-c4-p2-agent',
          },
        ],
      },
    },
  },
  client_announcement: {
    sync: true,
    description: 'A message to display to all clients. Should be set to empty string to disable.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  remote_agent_chat_model: {
    sync: true,
    description: 'Default chat model for remote-agent-mode chat requests, falls back to agent_chat_model if empty',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  remote_agent_chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for remote-agent-mode chat requests, falls back to agent_chat_fallback_model if empty',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  cli_agent_chat_model: {
    sync: true,
    description: 'Default chat model for CLI agent-mode chat requests, falls back to agent_chat_model if empty',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  cli_agent_chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for CLI agent-mode chat requests, falls back to agent_chat_fallback_model if empty',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  retrieval_tool_name: {
    sync: true,
    description: 'The name of the retrieval agent. Default is "query_rewrite". Invalid selection will result in server crashing during startup.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'query_rewrite',
          },
        ],
      },
    },
  },
  retrieval_tool_generation_model: {
    sync: true,
    description: 'LLM used by retrieval agent; one of the models deployed in agents-svc. Developed with claude-3-5-sonnet-v2; other model(s) added for experimentation. Invalid selection will result in using the deployed default.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-3-7-sonnet-lb',
          },
        ],
      },
    },
  },
  edit_file_tool_generation_model: {
    sync: true,
    description: 'LLM used by edit file agent; one of the models deployed in agents-svc. Developed with claude-3-5-sonnet-v2; other model(s) added for experimentation. Invalid selection will result in using the deployed default.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-3-7-sonnet-lb',
          },
        ],
      },
    },
  },
  enable_agents: {
    sync: true,
    description: 'If Agents endpoints are enabled for a given tenant or namespace',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_notifications: {
    sync: true,
    description: 'If true, notification endpoints are enabled for a given tenant or namespace',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  smartpaste_with_pure_additions: {
    sync: true,
    description: 'Improve smartpaste on pure additions.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_smart_paste: {
    sync: true,
    description: 'If Smart Paste is enabled for a given tenant or namespace',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  completion_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for completion requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  edit_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for edit requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 120000,
          },
        ],
      },
    },
  },
  chat_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for chat requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 300000,
          },
        ],
      },
    },
  },
  llm_generate_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for llm_generate requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 120000,
          },
        ],
      },
    },
  },
  codebase_retrieval_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for codebase_retrieval requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 40000,
          },
        ],
      },
    },
  },
  api_proxy_chat_retry_timeout_ms: {
    sync: true,
    description: 'The maximum time (in ms) allowed for all chat retry attempts at the api-proxy level. If this timeout is exceeded, retries will stop and the last error will be returned.',
    envs: {
      production: {
        rules: [
          {
            return_value: 20000,
          },
        ],
      },
    },
  },
  edit_file_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for edit_file requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 90000,
          },
        ],
      },
    },
  },
  upload_blob_content_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for upload_blob_content requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  batch_upload_blob_content_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for batch_upload_blob_content requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  find_missing_blobs_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for find_missing_blobs requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  checkpoint_blobs_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for checkpoint_blobs requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  enable_completion_load_balancing: {
    sync: true,
    description: 'If true, enable load balancing for completion requests made by api-proxy. Note that for this change to take effect you probably need to restart api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_client_version_blocked: {
    sync: true,
    description: 'If true for a client + version, block all requests from clients on that version.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_slow_payload_threshold_ms_completion: {
    sync: true,
    description: 'The maximum time a completion request payload can take to arrive before it is considered slow.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_slow_payload_threshold_ms_next_edit: {
    sync: true,
    description: 'The maximum time a next edit request payload can take to arrive before it is considered slow.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_check_tenant_data_access: {
    sync: true,
    description: 'If true, api proxy will check if the tenant has access to data stored in bigtable proxy.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
              'ysecurity-cmk',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_find_missing: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_checkpoint_blobs: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_client_metrics: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_report_error: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_record_user_events: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_preference_sample: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_edit_resolution: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit_resolution: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit_user_event: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_onboarding_session_event: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_resolve_completions: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_chat_feedback: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit_feedback: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_completion_feedback: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_edit: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_completion: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_memorize: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_agents: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',

    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_batch_upload_blob: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_get_models: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_chat: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              '8edb9096-4819-479d-8bb4-12b637c014e3',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_get_subscription_info: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_server_ban_rules_url_and_hash: {
    sync: true,
    description: 'URL and optional hash of the chat server ban rules. Hash and URL separated by comma.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: 'https://storage.googleapis.com/augment-bazel-data/public/blockexp_2025-04-11T03-26-12.txt',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  chat_server_filter_use_prefix_and_suffix: {
    sync: true,
    description: 'If true, use the prefix and suffix when filtering chat requests (non-strict)',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_server_strict_uses_prefix_and_suffix: {
    sync: true,
    description: 'If true, include the prefix and suffix when filtering chat requests with the strict regex',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_chat_stream: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              '8edb9096-4819-479d-8bb4-12b637c014e3',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_list_external_source_types: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },

  api_proxy_circuit_breaker_search_external_sources: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_throttle_completion: {
    sync: true,
    description: 'If true, throttle completion requests.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: 'dogfood-shard',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_throttle_completion_fill_rate: {
    sync: true,
    description: 'Fill rate for completion request token bucket (tokens per second)',
    envs: {
      production: {
        rules: [
          {
            return_value: 5.0,
          },
        ],
      },
    },
  },
  api_proxy_throttle_completion_capacity: {
    sync: true,
    description: 'Capacity for completion request token bucket (tokens)',
    envs: {
      production: {
        rules: [
          {
            return_value: 500,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat: {
    sync: true,
    description: 'If true, chat requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for chat requests if api_proxy_throttle_chat is true. The value is the number of tokens per second. Each token represents a single chat request.',
    envs: {
      production: {
        rules:
          apply_return_value(chat_banlist, 0.05)
          + [
            {
              namespace: self_serve_namespaces,
              return_value: 0.4,
            },
            {
              return_value: 0.8,
            },
          ],
      },
    },
  },
  api_proxy_throttle_chat_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for chat requests if api_proxy_throttle_chat is true. Each token represents a single chat request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  memory_classification_on_first_token: {
    sync: true,
    description: 'If true, memory classification happens on first token received rather than before sending a message.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  use_memory_snapshot_manager: {
    sync: true,
    description: 'If true, use the memory snapshot manager to get memories.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_agent: {
    sync: true,
    description: 'If true, agent requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_agent_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for chat requests if api_proxy_throttle_chat_agent is true. The value is the number of tokens per second. Each token represents a single chat request.',
    envs: {
      production: {
        rules:
          apply_return_value(chat_banlist, 0.05)
          + [
            {
              namespace: self_serve_namespaces,
              return_value: 0.4,
            },
            {
              return_value: 0.8,
            },
          ],
      },
    },
  },
  api_proxy_chat_cbf_failure_threshold: {
    sync: true,
    description: 'DISABLED - WILL PROBABLY MOVE TO THIRDPARTY PROXY - The failure threshold for the chat circuit breaker. If the error rate exceeds this value, the circuit breaker will trip.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1.0,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_agent_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for chat requests if api_proxy_throttle_chat_agent is true. Each token represents a single chat request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_remote_agent: {
    sync: true,
    description: 'If true, requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_remote_agent_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for remote agent chat requests if api_proxy_throttle_chat_remote_agent is true. The value is the number of tokens per second. Each token represents a single chat request.',
    envs: {
      production: {
        rules:
          apply_return_value(chat_banlist, 0.05)
          + [
            {
              namespace: self_serve_namespaces,
              return_value: 0.4,
            },
            {
              return_value: 0.8,
            },
          ],
      },
    },
  },
  api_proxy_throttle_chat_remote_agent_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for remote agent chat requests if api_proxy_throttle_chat_remote_agent is true. Each token represents a single chat request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_cli_agent: {
    sync: true,
    description: 'If true, CLI agent requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_cli_agent_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for CLI agent chat requests if api_proxy_throttle_chat_cli_agent is true. The value is the number of tokens per second. Each token represents a single chat request. For 500 messages per hour, this is approximately 0.139 tokens per second (500/3600).',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 0.139,  // ~500 messages per hour
          },
          {
            return_value: 0.139,  // ~500 messages per hour
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_cli_agent_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for CLI agent chat requests if api_proxy_throttle_chat_cli_agent is true. Each token represents a single chat request. This allows for burst capacity.',
    envs: {
      production: {
        rules: [
          {
            return_value: 500,  // Allow bursts of up to 500 messages
          },
        ],
      },
    },
  },
  api_proxy_chat_max_retries: {
    sync: true,
    description: 'The maximum number of retries for chat requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: 2,
          },
        ],
      },
    },
  },
  api_proxy_throttle_find_missing: {
    sync: true,
    description: 'If true, requests will be throttled -> fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_find_missing_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for find_missing requests if api_proxy_throttle_find_missing is true. The value is the number of tokens per second. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_throttle_find_missing_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for find_missing requests if api_proxy_throttle_find_missing is true. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 500000,
          },
        ],
      },
    },
  },
  api_proxy_throttle_upload: {
    sync: true,
    description: 'If true, requests will be throttled -> fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_upload_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for upload requests if api_proxy_throttle_find_missing is true. The value is the number of tokens per second. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_throttle_upload_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for upload requests if api_proxy_throttle_find_missing is true. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 500000,
          },
        ],
      },
    },
  },
  check_subscription_status: {
    sync: true,
    description: 'If true, api proxy will check the users subscription status and reject requests with no subscription',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_user_suspension_enabled: {
    sync: true,
    description: 'If true, api proxy will reject requests made by users with suspensions.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_trial_expiration_disclaimer: {
    sync: true,
    description: 'If true, show a disclaimer message to users whose trial is about to expire',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_trial_expiration_days_threshold: {
    sync: true,
    description: 'The number of days before trial expiration when the disclaimer should start showing',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  api_proxy_trial_version_enforcement_enabled: {
    sync: true,
    description: 'If true, enforce minimum client version requirements for trial users. If false, log violations but allow access (dry-run mode).',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_trial_min_vscode_version: {
    sync: true,
    description: 'Minimum VSCode extension version required for trial users. Empty string disables enforcement.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.509.1',
          },
        ],
      },
    },
  },
  api_proxy_trial_min_intellij_version: {
    sync: true,
    description: 'Minimum IntelliJ extension version required for trial users. Empty string disables enforcement.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.253.0',
          },
        ],
      },
    },
  },
  enforce_silent_request_validation: {
    sync: true,
    description: 'If true, reject silent requests with non-approved prompt patterns. If false, log warnings but allow requests (dry-run mode).',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_allow_similar_signups: {
    sync: true,
    description: 'If true, allow signups for users with similar emails',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_similar_signups_whitelist_domains: {
    sync: true,
    description: 'Comma separated list of domains to whitelist for similar signups',
    envs: {
      production: {
        rules: [
          {
            return_value: 'augm.io,turing.com,augmentcode.com',
          },
        ],
      },
    },
  },
  auth_central_get_user_uses_idp_user_id: {
    sync: true,
    description: 'If true, the auth central service will use the idp user id to get the user.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_verisoul_enabled: {
    sync: true,
    description: 'If true, the auth central service will use verisoul to check signups',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_verisoul_fail_on_invalid: {
    sync: true,
    description: 'If bit 0 is set, the auth central service will fail signups if verisoul returns an error. If bit 1 is set, the auth central service will fail logins if verisoul returns an error.',
    envs: {
      production: {
        rules: [
          {
            return_value: 3,
          },
        ],
      },
    },
  },
  auth_central_verisoul_multiple_user_threshold: {
    sync: true,
    description: 'The auth central service will fail signups if verisoul returns a number of accounts linked greater than or equal to this threshold. A value of 0 disables this check.',
    envs: {
      production: {
        rules: [
          {
            return_value: 40,
          },
        ],
      },
    },
  },
  auth_central_bearer_token_auth_enabled: {
    sync: true,
    description: 'If true, the auth central service will process Authorization bearer tokens for test user authentication',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  customer_ui_e2e_mode_enabled: {
    sync: true,
    description: 'If true, enables e2e testing mode in customer UI with test-specific behaviors like bypassing payment validation and enabling test payment methods',
    envs: {
      production: {
        rules: [
          {
            env: ['STAGING'],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_inactive_subscription_disclaimer: {
    sync: true,
    description: 'If true, inject a message to users whose subscription is inactive in chat and agent mode. check_subscription_status must also be enabled for this to take effect. If this flag is false while check_subscription_status is true, the user will receive a 402 error instead when their subscription is inactive.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enforce_usage_credits: {
    sync: true,
    description: 'If true, inject a message to users whose subscription is out of user message credits in chat and agent mode and do not let them make requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_billing_for_remote_agents: {
    sync: true,
    description: 'If true, count remote-agent messages for billing',
    envs: {
      production: {
        rules: [
          {
            namespace: ['staging-shard-0'] + self_serve_namespaces,
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_server_side_idempotency_key: {
    sync: true,
    description: 'If true, compute server side idempotency key for billing using combination of request-id and message content, instead of relying on client provided reqeuest-id as idempotency key. This is to prevent the abuse of request-id to get free api calls. Adding this feature flag for a safe rollout. Once rolled out to all environments, we can remove this flag.',
    envs: {
      production: {
        rules: [
          {
            namespace: ['staging-shard-0'],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_external_sources_in_chat_min_version: {
    sync: true,
    description: 'The minimum version that we enable external sources (like documentation sets) for in chat in vscode.  This is being used to rollout the feature. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.243.2',
          },
        ],
      },
    },
  },
  embeddings_search_replicate_cache_on_startup: {
    sync: true,
    description: 'If true, get cache state form peer embeddings searchers on startup.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  embeddings_search_max_replication_age_s: {
    sync: true,
    description: 'Relative age of entries to replicate. Set to 0 to replicate all cache entries.',
    envs: {
      production: {
        rules: [
          {
            namespace: 'staging-shard-0',
            return_value: 259200,  // roughly 3 days (w/o DST or leap seconds)
          },
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  intellij_chat_min_version: {
    sync: true,
    description: 'Sets the minimum version number of intellij to allow chat. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to the old enable_intellij_chat flag',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.73',
          },
          {
            tenant_name: [
              'advisor360',
              'chainalysis',
              'paystone',
              'plejd',
              'purestorage',
              'realtor',
              'bitwarden',
              'rewst',
              'redis',
              'webflow',
              'samaya',
              'bighatbio',
              'sanity',
              'river',
              'ritchiebros',
              'gladly',
              'pocketlaw',
            ],
            return_value: '0.2.0',
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: '0.0.73',
          },
          {
            tenant_name: [
              'i0-vanguard0',
            ],
            return_value: '0.1.4',
          },
          {
            return_value: '0.12.0',
          },
        ],
      },
    },
  },
  bypass_language_filter: {
    sync: true,
    description: 'DEPRECATED- rolled out. If true, allow the client extensions to upload files regardless of the language.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  indexer_retry_max_retries: {
    sync: true,
    description: 'The number of times to retry indexing a blob. This helps to avoid that retryable errors during indexing (timeouts in the embedders) fail the entire indexing of a blob. The plan is to roll this out to all tenants once we validated that it works in dogfood.',
    envs: {
      production: {
        rules: [
          {
            return_value: 7,
          },
        ],
      },
    },
  },
  embeddings_indexer_upload_timeout_seconds: {
    sync: true,
    description: 'The timeout (in seconds) for uploading transformed content to content manager. Longer timeout can help mitigate wasted work on large blobs when embedders are saturated.',
    envs: {
      production: {
        rules: [
          {
            return_value: 300,
          },
        ],
      },
    },
  },
  enable_hindsight: {
    sync: true,
    description: 'Enable Hindsight data collection for the namespace. This flag should be enabled only for those namespaces where user data collection is allowed, such as with contractors and Vanguard. The flag is permanent, and will be rolled out to Vanguard after a short period of time on Dogfood.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            namespace: [
              'i0',
              'i1',  // self serve vanguard is also trainable
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_upload_size_bytes: {
    sync: true,
    description: 'Set the recommended maximum file size a client should upload for retrieval. This flag allows us to increase the default max file size from 128KB to 512KB, or larger, in a controlled way. The plan is to rollout the increase in incremental steps (128KB, 256KB, 512KB, etc.) across dogfood, Vanguard, and then all customers.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 524288,
          },
          {
            return_value: 524288,
          },
        ],
      },
    },
  },
  intellij_force_completion_min_version: {
    sync: true,
    description: 'Sets the minimum version number of intellij to allow forcing completions on all edits. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will do nothing.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '379fa71c2fda84c36fb3744d9b8403be6c06271bd6073ce22cbc32d4f3d1391e',
            ],
            return_value: '',
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: '',
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.5.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'pocketlaw',
              'montecarlodata',
              'sigma',
            ],
            return_value: '0.10.0',
          },
          {
            return_value: '0.68.0',
          },
        ],
      },
    },
  },
  vscode_next_edit_ux1_max_version: {  // temporarily we keep everyone on old experience. This will go away soon
    sync: true,
    description: 'ux1 for next-edit per vscode version',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '634e3c4fb2c5a494a08ebadf9fcd315267876910189eef01992f60fc92268a86',
              'af3bc9ae9872910a9d958c4abc5c08014ac5bbaaf57d4d110d07db3e6b8d07aa',
              '56d407b5f2c9f8baf8b69bf69a4ddeec8ff47c10b861b646e8e25e29bf7eb176',
              'ced4d412e80feac9ef0956e8a4775c9350cf940e4681bbb16fcf88a84b2bbbfa',
            ],
            return_value: '0.338.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'discovery0',
              'ampsortation',
              'observeinc',
              'montecarlodata',
              'reveart',
              'collective',
              'webflow',
            ],
            return_value: '0.338.0',
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
            ],
            return_value: '0.338.0',
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.331.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_next_edit_ux2_max_version: {  // temporarily we keep everyone on old experience. This will go away soon
    sync: true,
    description: 'ux2 for next-edit per vscode version',

    // wave-1 users will at some point go here with value 999.0.0
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              'a53aeb555d81788f81755da5ba93866e1d0ba22691975036c71fb33c3664f4dd',
              '12d1694744c5bd7c79589ad1ae75fefb4a6520740df98a27e09829c747d51452',
              '4ccdda635e9a53b53e418e6e055543365c0df43590feae81d9fa0aa061eb5045',
            ],
            return_value: '0.341.0',
          },
          {
            user_id_hmac: [
              '634e3c4fb2c5a494a08ebadf9fcd315267876910189eef01992f60fc92268a86',
              'af3bc9ae9872910a9d958c4abc5c08014ac5bbaaf57d4d110d07db3e6b8d07aa',
              '56d407b5f2c9f8baf8b69bf69a4ddeec8ff47c10b861b646e8e25e29bf7eb176',
              'ced4d412e80feac9ef0956e8a4775c9350cf940e4681bbb16fcf88a84b2bbbfa',
            ],
            return_value: '0.341.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'discovery0',
              'ampsortation',
              'observeinc',
              'montecarlodata',
              'reveart',
              'collective',
              'webflow',
            ],
            return_value: '0.341.0',
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
            ],
            return_value: '0.341.0',
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.331.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_next_edit_bottom_panel_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to allow next-edit bottom panel. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to enabled_debug_features on the extension',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            cloud: [
              'GCP_US_CENTRAL1_PROD',
            ],
            return_value: '0.394.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_direct_apply_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to allow direct apply of code changes from codeblocks without going through the diff view. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'staging-shard-0',
            ],
            return_value: '0.393.0',
          },
          {
            return_value: '0.499.0',
          },
        ],
      },
    },
  },
  vscode_next_edit_min_version: {  //decides if next-edit on or off
    sync: true,
    description: 'Sets the minimum version number of vscode to allow next-edit. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to enabled_debug_features on the extension',
    envs: {
      production: {
        rules: [
          // wave-1
          {
            tenant_name: [
              'ampsortation',
              'observeinc',
              'montecarlodata',
              'reveart',
              'collective',
              'webflow',
            ],
            return_value: '0.282.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'discovery0',
            ],
            return_value: '0.250.0',
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
            ],
            return_value: '0.228.0',
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.215.0',
          },
          {
            tenant_name: [
              'collectors',
              'humaninterest',
              'onelineage',
              'eikon',
              'ddn',
              'guideline',
              'zerity',
              'purestorage',
              'lemonade',
              'advisor360',
              'sifive',
              'realtor',
              'quorum',
              'enfabrica',
              'sigma',
              'afresh',
              'newfront',
              'filevine',
            ],
            return_value: '0.343.0',
          },
          {
            cloud: [
              'GCP_US_CENTRAL1_PROD',
            ],
            return_value: '0.343.0',
          },
          {
            namespace: [
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'e0',
              'e1',
              'e10',
              'e11',
              'e2',
              'e3',
              'e4',
              'e5',
              'e6',
              'e7',
              'e8',
              'e9',
              'flume',
              'i0',
              'i1',
              'lmnt',
              'newfront',
              'nucleus',
              'onebuild',
              'paystone',
              'pocketlaw',
              'pollyex',
              'roboto',
              'xlb',
              'xlc',
              'xle',
            ],
            return_value: '0.343.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_flywheel_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to allow flywheel features. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to enabled_debug_features on the extension',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'augment-test-1',
            ],
            return_value: '0.0.0',
          },
          {
            user_id_hmac: [
              'ab4e6618667e1253d4a1be7ad88d2d1bbca9b95b9cbb70cf26ce29e726d2aaa2',
              'b8bc06a8e0733ded6c57fd47b6ee7b1cf1c1b86850d3917d756b43483448c5ac',
              '7824434b6c3834a2d74a41675f6d880fdf48ec2ee02f54ba5386967c1a02bf4d',
              '24e2294ffea3086215eb5df598c50e04f9811ebfd67d4e06ccb590e3c66d184f',
              '1584bd5544ac0cec85b42f9865d00b58d83adda5fe7211e2b51aeeb909cb808c',
              '7b9945ce0cfbfee179e125f974e597e6baedea0c7c56e033cdf54a8ba3d0c78b',
              '8cb88cb236fc5f50b83cc26daf89e1675976120c3b78146a35287b2431a5e0d9',
              '67e5994487fecb480bd6413e104f89d3193ade9028f7f92a1ef4a8a8847af7ef',
              '03dd024f2ae8bfeec997b90fe096506eaf1d06769cb718099f7d888d3813c997',
            ],
            return_value: '0.234.0',
          },
          {
            return_value: '0.282.0',
          },
        ],
      },
    },
  },
  enable_content_manager_subscriptions_group3: {
    sync: true,
    description: 'Enables content manager subscriptions to pull blobs from the group3 queue. This queue is currently used by docsets and catchup indexing. This flag is a failsafe in case docsets cause too much load, to give us a way to stop pulling blobs from the group3 queue. During normal operation, this should be true everywhere.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  content_manager_pull_messages_batch_size: {
    sync: true,
    description: 'Positive value enables using pull instead of streaming pull. Sets the batch size for pull.',
    envs: {
      production: {
        rules: [
          {
            return_value: 16,
          },
        ],
      },
    },
  },
  enable_auto_docsets: {
    sync: true,
    description: 'If true, docsets are automatically added in addition to the at-mentioned docsets to improve chat quality.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_docset_uploads: {
    sync: true,
    description: 'Enables docset uploads. If this is true, then the docsets in the docset.jsonnet file will be uploaded to content manager (and indexed). If false, the docset server will pretend it has no docsets. This flag is used to stage our rollout of docset uploads, to avoid overwhelming the indexing pipeline, particularly embedders. Once the initial docset list is uploaded to all namespaces, we can remove this flag.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_completion_embeddings_search_cancel: {
    sync: true,
    description: 'If true, the embeddings search service can cancel when a more recent completion request is submitted.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  next_edit_low_quality_threshold_strict: {
    sync: true,
    description: 'A stringent threshold for identifying low-quality edits in the next-edit feature. 1.0 for no threshold.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'dogfood-shard',
            ],
            model_name: [
              'raven-edit-v3-15b',
            ],
            // TODO(vzhao): This value should be specific to the model.
            // The filter rate is 40%.
            // See services/next_edit_host/server/prism_models/README.md
            return_value: '0.85',
          },
          {
            return_value: '1.0',
          },
        ],
      },
    },
  },
  slackbot_max_response_latency_secs: {
    sync: true,
    description: "The maximum time, in seconds, between a Slack message from a user and when the Slackbot will try to respond. If more than this amount of time has passed the message will be dropped. This value should be kept large enough that clock skew isn't a concern.",
    envs: {
      production: {
        rules: [
          {
            return_value: 600,
          },
        ],
      },
    },
  },
  github_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the github processor will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail, then set back to false once all of those messages have been processed.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  github_process_reregister_on_failed_diff: {
    sync: true,
    description: 'If true, the github processor will reregister the repo when diff fails to apply. The intention is that this will be set temporarily when we may need to clear some bad existing state, or if there are known bugs in diff handling.',
    envs: {
      production: {
        rules: [
          {
            // TODO: There is an issue that we seem to run into occasionally with
            // diffs. It seems to always happen for larger files, but I'm not exactly
            // sure what the bug is or why these diffs are failing. This doesn't
            // happen that often, so reregister on these failures until we have time
            // to debug and do a real fix.
            return_value: true,
          },
        ],
      },
    },
  },
  ri_support_database_exporter_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the RI support database exporter will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_share_min_version: {
    sync: true,
    description: 'Minimum version required for a VSCode client to use the Share service. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.284.0',
          },
          {
            namespace: [
              'aitutor-mercor',
              'aitutor-turing',
            ],
            return_value: '0.314.0',
          },
          {
            namespace: [
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: '',  // Explicitly disable sharing for discovery and community tenants (AU-6267)
          },
          {
            return_value: '0.314.0',
          },
        ],
      },
    },
  },
  intellij_share_min_version: {
    sync: true,
    description: 'Minimum version required for a Intellij client to use the Share service. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.58.0',
          },
          {
            namespace: [
              'aitutor-mercor',
              'aitutor-turing',
            ],
            return_value: '0.87.0',
          },
          {
            namespace: [
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: '',  // Explicitly disable sharing for discovery and community tenants (AU-6267)
          },
          {
            return_value: '0.87.0',
          },
        ],
      },
    },
  },
  enable_smart_paste_min_version: {
    sync: true,
    description: 'Minimum version required for Smart Paste feature',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.267.0',
          },
          {
            return_value: '0.267.0',
          },
        ],
      },
    },
  },
  workspace_guidelines_length_limit: {
    sync: true,
    description: 'The maximum length of Workspace Guidelines. We use this value to reject over-length Workspace Guidelines on the frontend. Do not decrease this value because it could disrupt existing guidelines users have been using.',
    envs: {
      production: {
        rules: [
          {
            return_value: 49512,
          },
        ],
      },
    },
  },
  user_guidelines_length_limit: {
    sync: true,
    description: 'The maximum length of User Guidelines. We use this value to reject over-length User Guidelines on the frontend. Do not decrease this value because it could disrupt existing guidelines users have been using.',
    envs: {
      production: {
        rules: [
          {
            return_value: 24576,
          },
        ],
      },
    },
  },
  intellij_preference_collection_allowed_min_version: {
    sync: true,
    description: 'If true, the intellij client will allow preference collection.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: '0.226.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  retrieval_embedder_multiplex: {
    sync: true,
    description: 'Enable multiplexing over multiple embedder clients during the retrieval process',
    envs: {
      production: {
        // note: eu namespaces are not configured in multiplex mode and will always use the default embedder
        rules: [
          {
            model_name: [
              'chatanol-qwen-v1-1',
              'chatanol-qwen-v1-1-commits',
            ],
            return_value: '{"default": 1.0, "gsc": 0}',
          },
          {
            return_value: '{"default": 0, "gsc": 1.0}',
          },
        ],
      },
    },
  },
  indexer_embedder_multiplex: {
    sync: true,
    description: 'Enable multiplexing over multiple embedder clients during the indexing process',
    envs: {
      production: {
        // note: eu namespaces are not configured in multiplex mode and will always use the default embedder
        rules: [
          {
            model_name: [
              'chatanol-qwen-v1-1',
              'chatanol-qwen-v1-1-commits',
            ],
            return_value: '{"default": 1.0, "gsc": 0}',
          },
          {
            return_value: '{"default": 0.0, "gsc": 1.0}',
          },
        ],
      },
    },
  },
  completion_inferer_multiplex: {
    sync: true,
    description: 'Enable multiplexing over multiple inference hosts for completion models',
    envs: {
      production: {
        // We limit GSC use to us-central only right now to sidestep EU configuration.
        // TODO(carl): extend to EU support.
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'pre-prod',
            ],
            return_value: '',
          },
          {
            cloud: [
              'GCP_US_CENTRAL1_PROD',
            ],
            return_value: '{"default": 1.0, "gsc": 0.0}',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  max_trackable_file_count: {
    sync: true,
    description: 'The maximum number of files in a trackable source folder. The front end will refuse to track folders with more than this many files. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 250000,
          },
          {
            tenant_name: [
              'dogfood-shard',
              'adobe',
            ],
            return_value: 1000000,
          },
          {
            return_value: 400000,
          },
        ],
      },
    },
  },
  max_trackable_file_count_without_permission: {
    sync: true,
    description: 'The maximum number of files in a source folder before the front will request permission to track it. A value that is greater than or equal to max_trackable_file_count effectively disables this check, as the max_trackable_file_count check takes precedence. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'i0',
              'i1',
            ],
            return_value: 0,
          },
          {
            return_value: 150000,
          },
        ],
      },
    },
  },
  min_uploaded_percentage_without_permission: {
    sync: true,
    description: 'For the purpose of determining whether to request permission to sync a workspace, this value is the minimum percentage of files in a workspace that must have been uploaded for the front end to consider the workspace itself to be uploaded. If less than this percentage of files have been uploaded, the front end will request permission to sync the workspace. A value of 0 causes all workspaces to be considered uploaded and therefore effectively disables this check. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            return_value: 90,
          },
        ],
      },
    },
  },
  next_edit_debounce_ms: {
    sync: true,
    description: 'Debounce time in milliseconds for next edit',
    envs: {
      production: {
        rules: [
          {
            return_value: 400,
          },
        ],
      },
    },
  },
  enable_completion_file_edit_events: {
    sync: true,
    description: 'If true, the client will enable file edit event collection for completions and send to the backend',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_enable_cpu_profile: {
    sync: true,
    description: 'Enables CPU profiling in VS Code extension',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  verify_folder_is_source_repo: {
    sync: true,
    description: "If true, the front end will request permission to sync source folders that don't appear to be source repos (for example, if they don't have a .git directory or a .augmentroot file). (Currently vscode extension only.)",
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  refuse_to_sync_home_directories: {
    sync: true,
    description: "If true, the front end will refuse to sync a user's home directory. (Currently vscode extension only.)",
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_file_limits_for_syncing_permission: {
    sync: true,
    description: 'If true, the front end will honor the max_trackable_file_count and max_trackable_file_count_without_permission feature flags. If false, it will not count the number of files in a folder, or enforce any size limits when determining whether to request permission to sync it. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_chat_mermaid_diagrams: {
    sync: true,
    description: 'DEPCREATED - superseded by min_version forms. If true, the front end will render Mermaid codeblocks as Mermaid diagrams in the Chat.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  longest_overlap_lm_model: {
    sync: true,
    description: 'Selects the implementation for the longest overlap language model',
    envs: {
      production: {
        rules: [
          {
            return_value: 'rust',
          },
        ],
      },
    },
  },
  longest_overlap_lm_max_tokens_to_speculate: {
    sync: true,
    description: 'The maximum number of tokens a non-neural model can speculate ahead',
    envs: {
      production: {
        rules: [
          {
            model_name: [
              'forger-smart-paste-sc2-7b-32k',
              'forger-smart-paste-v2-qwen-8b-32k',
              'forger-v2-qwen-14b-q-32k',
            ],
            return_value: 127,
          },
          {
            return_value: 15,
          },
        ],
      },
    },
  },
  generation_state_max_tokens_to_speculate_ahead: {
    sync: true,
    description: 'The maximum number of tokens a neural model can speculate ahead',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 1,
          },
          {
            return_value: 1,
          },
        ],
      },
    },
  },
  generation_state_min_speculation_probability: {
    sync: true,
    description: 'The minimum joint probability of speculated tokens for a neural speculation model to generate another token',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 0.0,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  generation_state_generate_with_main_model_first: {
    sync: true,
    description: 'If true, the first generated token will be from the main model',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  generation_state_neural_speculation_enabled: {
    sync: true,
    description: 'If true, neural speculation is enabled',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_blocked_countries: {
    sync: true,
    description: 'Comma separated list of countries to block signups from',
    envs: {
      production: {
        rules: [
          {
            return_value: 'CU,IR,KP,RU,CN',
          },
        ],
      },
    },
  },
  auth_central_disable_second_signup_flow: {
    sync: true,
    description: 'If true, the second signup flow (/signup/login and /signup/callback) is disabled',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_signup_max_burst: {
    sync: true,
    description: 'The maximum number of signups allowed in an instant',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  auth_central_signups_per_day: {
    sync: true,
    description: 'The maximum number of signups allowed per day',
    envs: {
      production: {
        rules: [
          {
            return_value: 24000,
          },
        ],
      },
    },
  },
  auth_central_implicit_tos_acceptance: {
    sync: true,
    description: 'If true, HTML auth flow auto-submits ToS acceptance (implicit acceptance) after collecting security signals.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_signup_tenant: {
    sync: true,
    description: 'The tenant to use for signups',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 'staging-vanguard0',
          },
          {
            namespace: [
              'central',
            ],
            return_value: 'i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  auth_central_signup_done_redirect: {
    sync: true,
    description: 'The callback URL to use for open source signups',
    envs: {
      production: {
        rules: [
          {
            return_value: 'https://www.augmentcode.com/opensource/registration',
          },
        ],
      },
    },
  },
  auth_central_individual_redirect: {
    sync: true,
    description: 'The callback URL to use for individual signups',
    envs: {
      production: {
        rules: [
          {
            return_value: 'https://www.augmentcode.com/registration',
          },
        ],
      },
    },
  },
  auth_central_individual_tenant: {
    sync: true,
    description: 'The tenants to use for individual signups - comma separated list',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 'staging-discovery0',
          },
          {
            namespace: [
              'central',
            ],
            return_value: 'discovery1,d1-discovery1,d1-discovery2,d1-discovery3,d1-discovery4,d1-discovery5,d1-discovery6,d1-discovery7,discovery2,d2-discovery1,d2-discovery2,d2-discovery3,d2-discovery4,d2-discovery5,d2-discovery6,d2-discovery7,discovery3,d3-discovery1,d3-discovery2,d3-discovery3,d3-discovery4,d3-discovery5,d3-discovery6,d3-discovery7,d4-discovery0,d4-discovery1,d4-discovery2,d4-discovery3,d4-discovery4,d4-discovery5,d4-discovery6,d4-discovery7,d5-discovery0,d5-discovery1,d5-discovery2,d5-discovery3,d5-discovery4,d5-discovery5,d5-discovery6,d5-discovery7,d6-discovery0,d6-discovery1,d6-discovery2,d6-discovery3,d6-discovery4,d6-discovery5,d6-discovery6,d6-discovery7,d7-discovery0,d7-discovery1,d7-discovery2,d7-discovery3,d7-discovery4,d7-discovery5,d7-discovery6,d7-discovery7,d8-discovery0,d8-discovery1,d8-discovery2,d8-discovery3,d8-discovery4,d8-discovery5,d8-discovery6,d8-discovery7,d9-discovery0,d9-discovery1,d9-discovery2,d9-discovery3,d9-discovery4,d9-discovery5,d9-discovery6,d9-discovery7,d10-discovery0,d10-discovery1,d10-discovery2,d10-discovery3,d10-discovery4,d10-discovery5,d10-discovery6,d10-discovery7,d11-discovery0,d11-discovery1,d11-discovery2,d11-discovery3,d11-discovery4,d11-discovery5,d11-discovery6,d11-discovery7,d12-discovery0,d12-discovery1,d12-discovery2,d12-discovery3,d12-discovery4,d12-discovery5,d12-discovery6,d12-discovery7,d13-discovery0,d13-discovery1,d13-discovery2,d13-discovery3,d13-discovery4,d13-discovery5,d13-discovery6,d13-discovery7,d14-discovery0,d14-discovery1,d14-discovery2,d14-discovery3,d14-discovery4,d14-discovery5,d14-discovery6,d14-discovery7,d15-discovery0,d15-discovery1,d15-discovery2,d15-discovery3,d15-discovery4,d15-discovery5,d15-discovery6,d15-discovery7,d16-discovery0,d16-discovery1,d16-discovery2,d16-discovery3,d16-discovery4,d16-discovery5,d16-discovery6,d16-discovery7,d17-discovery0,d17-discovery1,d17-discovery2,d17-discovery3,d17-discovery4,d17-discovery5,d17-discovery6,d17-discovery7,d18-discovery0,d18-discovery1,d18-discovery2,d18-discovery3,d18-discovery4,d18-discovery5,d18-discovery6,d18-discovery7,d19-discovery0,d19-discovery1,d19-discovery2,d19-discovery3,d19-discovery4,d19-discovery5,d19-discovery6,d19-discovery7,d20-discovery0,d20-discovery1,d20-discovery2,d20-discovery3,d20-discovery4,d20-discovery5,d20-discovery6,d20-discovery7',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  auth_central_login_invitations_enabled: {
    sync: true,
    description: 'If true, a list of invitations will be rendered during sign in, if any exist for the user signing in',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_recaptcha_threshold: {
    sync: true,
    description: 'The reCAPTCHA threshold to reject a sign-up. Negative disable reCaptcha. 0.0 allows all sign-ups but still logs.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.3,
          },
        ],
      },
    },
  },
  auth_central_verosint_fingerprinting: {
    sync: true,
    description: 'If true, the auth central will collect a Verosint fingerprint if it is collecting fingerprints.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_verosint_reporting: {
    sync: true,
    description: 'If true, the auth central will report user activity to Verosint and publish reports to RI.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_hcaptcha_collect: {
    sync: true,
    description: 'If true, the auth central will display hCaptcha challenges',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_hcaptcha_eval: {
    sync: true,
    description: 'If true, the auth central will evaluate hCaptcha responses',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_hcaptcha_block: {
    sync: true,
    description: 'If true, the auth central will block sign-ups if the hCaptcha response is invalid',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_chat_hint_decoration_min_version: {
    sync: true,
    description: 'When enabled, a decoration is shown hinting to open the chat',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.274.0',
          },
        ],
      },
    },
  },
  vscode_new_threads_menu_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VSCode client to use the new chat thread UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.305.0',
          },
        ],
      },
    },
  },
  intellij_new_threads_menu_min_version: {
    sync: true,
    description: 'Defines the minimum version of the Intellij client to use the new chat thread UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.77.0',
          },
        ],
      },
    },
  },
  enable_new_threads_list: {
    sync: true,
    description: 'If true, enables the new threads list functionality. This flag can be used independently or in combination with enableBackgroundAgents to control the doUseNewDraftFunctionality behavior.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_untruncated_content_storage: {
    sync: true,
    description: 'If true, enables storage of untruncated content for retrieval tools',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_lines_terminal_process_output: {
    sync: true,
    description: 'Maximum number of lines to show from the end of truncated output (0 = use default truncation)',
    envs: {
      production: {
        rules: [
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  intellij_show_summary: {
    sync: true,
    description: 'If true, the IntelliJ client will show the codebase summary in chat after the first sync.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_editable_history_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VSCode client to use the editable history feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
            ],
            return_value: '0.305.0',
          },
          {
            return_value: '0.330.0',
          },
        ],
      },
    },
  },
  // don't delete needed for backwards compatibility
  enable_guidelines: {
    sync: true,
    description: 'If true, the guidelines feature is enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  // don't delete needed for backwards compatibility
  intellij_enable_user_guidelines: {
    sync: true,
    description: 'If true, the intellij client will enable the user guidelines feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            client: 'intellij',
            min_client_version: '0.197.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  // don't delete needed for backwards compatibility
  intellij_user_guidelines_in_settings: {
    sync: true,
    description: 'If true, the intellij client will enable the user guidelines feature in the settings page.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            client: 'intellij',
            min_client_version: '0.197.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  // don't delete needed for backwards compatibility
  intellij_enable_workspace_guidelines: {
    sync: true,
    description: 'If true, the intellij client will enable the workspace guidelines feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            client: 'intellij',
            min_client_version: '0.197.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_rules: {
    sync: true,
    description: 'If true, the .augment/rules feature is enabled.',
    envs: {
      production: {
        rules: [
          {
            // Specific users for whom we know rules are currently
            // broken (AU-12093)
            user_uuid: [
              'bd86f19a-00ea-4b5c-ac00-a74e81997b43',
              '71ab4827-a93b-48fe-9fb6-b579277ca527',
            ],
            return_value: false,
          },
          {
            // VSCode support starting in 0.492.0
            client: 'vscode',
            max_client_version: '0.491.999',
            return_value: false,
          },
          {
            user_uuid: [
              'e4b47ab5-d2ab-4291-bbe6-2e8fd9bab542',
              '567f57b6-2a35-461a-970a-61d0c2606bdc',
            ],
            client: 'intellij',
            min_client_version: '0.249.1',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'intellij',
            min_client_version: '0.249.1',
            return_value: true,
          },
          {
            // Enable rules for specific tenants in IntelliJ
            tenant_name: [
              'athenahealth',
              'bigid',
              'empower',
              'intercom',
              'wiz',
            ],
            client: 'intellij',
            min_client_version: '0.249.1',
            return_value: true,
          },
          {
            // Limited/no support in intellij, vim, or cli agent
            client: ['intellij', 'vim', 'cli'],
            return_value: false,
          },
          {
            // client: python (just an SDK wrapper over the API)
            // backend
            return_value: true,
          },
        ],
      },
    },
  },
  codebase_retrieval_budget: {
    sync: true,
    description: 'The backend character budget for codebase retrieval requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: 20000,
          },
        ],
      },
    },
  },
  codebase_retrieval_add_line_numbers: {
    sync: true,
    description: 'If true, line numbers are added to the codebase retrieval output.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_use_checkpoint_manager_context_min_version: {
    sync: true,
    description: 'The minimum version of the vscode client to rely on checkpoint manager context when computing request context. Legacy behavior is to scan the entire workspace to collect blob names for each request.',

    // Behavior is functional in 0.319.0; we switch most users over in a later version so we can tell from user
    // agent whether the client is using the new method without checking the flags received at startup.
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '0.319.0',
          },
          {
            return_value: '0.323.0',
          },
        ],
      },
    },
  },
  vscode_validate_checkpoint_manager_context: {
    sync: true,
    description: 'Paired with flag "vscode_use_checkpoint_manager_context_min_version". If both flags are enabled, then both new and legacy methods will be performed and the results will be compared to ensure they match.',
    envs: {
      production: {
        rules: [
          {
            // Note: All namespaces ran with validation for minimum 7 business days
            // before disabling.
            // If re-enabling validation for any large workspace (>50k files), we
            // should probably do so for only a fraction of requests, as it would
            // constitute a notable regression
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_enable_chat_mermaid_diagrams_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client where the front end will render Mermaid codeblocks as Mermaid diagrams in the Chat.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.314.0',
          },
        ],
      },
    },
  },
  intellij_completions_history_min_version: {
    sync: true,
    description: 'Defines the minimum version of the Intellij client to use the completions history feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  enable_glean: {
    sync: true,
    description: 'If true, users can sign-in to Glean and all slackbot requests will call the glean API to get relevant documents and add them to the prompt. This is deployed only in staging or only for friendly customers.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'dogfood-shard',
              'collectors',
              'ddn',
              'webflow',
              'intuit',
              'realtor',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_task_list_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to enable the task list functionality. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.482.0',
          },
        ],
      },
    },
  },
  intellij_task_list_min_version: {
    sync: true,
    description: 'Sets the minimum version number of intellij to enable the task list functionality. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.257.0',
          },
        ],
      },
    },
  },
  vscode_support_tool_use_start_min_version: {
    sync: true,
    description: 'Enable the use of TOOL_USE_START nodes in the vscode client.  The chatStream can use this to get notified when a tool use is started.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.485.0',
          },
        ],
      },
    },
  },
  slackbot_enable_v2_formatter: {
    sync: true,
    description: 'If true, use the v2 slackbot prompt formatter. Now that this has been enabled everywhere, we should remove this flag and the old formatter in the near future.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'collectors',
              'dogfood-shard',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  // Expected to be short-lived, as the client does pass this in ChatFeatureDetectionFlags
  // Just here to roll out to staging before enabling in prod
  chat_generate_tool_use_start: {
    sync: true,
    description: 'If true, TOOL_USE_START nodes may be returned by Chat/ChatStream to clients that also support it',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  chat_reprompt_empty_response: {
    sync: true,
    description: 'If true, chat requests that produce no output will be retried once with a continuation prompt',
    envs: {
      production: {
        rules: [
          {
            // If we're happy with this feature, we can move to it being deploy-time
            // and configured as part of the model config
            model_name: [
              'claude-sonnet-4-0-200k-v7-c4-p2-agent',
              'claude-sonnet-4-0-200k-v8-c4-p2-agent',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_postprocess_code_block_filter_enabled: {
    sync: true,
    description: 'If true, Sentry postprocessing will only run on chat responses that contain code blocks (triple backticks or <augment_code_snippet> tags). This optimization reduces unnecessary postprocessing for regular text responses.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  smart_paste_precompute_mode: {
    sync: true,
    description: "Controls smart paste precomputation: 'off' (never), 'visible-hover' (default, on hover), 'visible' (when visible), 'on' (immediate)",
    envs: {
      production: {
        rules: [
          {
            return_value: 'visible',
          },
        ],
      },
    },
  },
  intellij_smart_paste_min_version: {
    sync: true,
    description: 'Defines the minimum version of the Intellij client to enable smart paste.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.119.0',
          },
        ],
      },
    },
  },
  vscode_design_system_rich_text_editor_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to use the new design system rich text editor component.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.363.0',
          },
        ],
      },
    },
  },
  intellij_design_system_rich_text_editor_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to use the new design system rich text editor component.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.146.0',
          },
        ],
      },
    },
  },
  allow_client_feature_flag_overrides: {
    sync: true,
    description: 'If true, the client can override feature flags received from the server. DO NOT ENABLE THIS OUTSIDE OF DOGFOOD',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_chat_with_tools_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable the use of tools in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_chat_with_tools_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable the use of tools in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_chat_multimodal_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to use chat multimodal features.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.373.0',
          },
          {
            return_value: '0.384.0',
          },
        ],
      },
    },
  },
  intellij_chat_multimodal_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to use chat multimodal features.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.207.0',
          },
        ],
      },
    },
  },
  intellij_enable_chat_mermaid_diagrams_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client where the front end will render Mermaid codeblocks as Mermaid diagrams in the Chat.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.128.0',
          },
        ],
      },
    },
  },
  vscode_agent_edit_tool: {
    sync: true,
    description: 'Specifies which edit tool to use in VSCode agent mode.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'str_replace_editor_tool',
          },
        ],
      },
    },
  },
  vscode_agent_mode_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable agent mode in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
        ] + apply_return_value(
          agents_rubrik_allowlist,
          '0.399.1'
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '0.477.1',
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: '0.367.0',
          },
        ] + apply_return_value(
          agents_wave2_latest +
          agents_wave2_stable,
          '0.373.0'
        ) + apply_return_value(
          agents_wave3_latest +
          agents_wave3_stable,
          '0.380.0'
        ) + apply_return_value(
          agents_wave4_latest +
          agents_wave4_stable,
          '0.387.0'
        ) + [
          {
            return_value: '0.395.0',
          },
        ],
      },
    },
  },
  vscode_agent_mode_min_stable_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable agent mode in chat conversations for stable (not pre-release) versions.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
        ] + apply_return_value(
          agents_rubrik_allowlist,
          '0.399.1'
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '0.477.1',
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: '0.367.0',
          },
        ] + apply_return_value(
          agents_wave2_latest +
          agents_wave2_stable,
          '0.373.0'
        ) + apply_return_value(
          agents_wave3_latest +
          agents_wave3_stable,
          '0.380.0'
        ) + apply_return_value(
          agents_wave4_latest +
          agents_wave4_stable,
          '0.387.0'
        ) + [
          {
            return_value: '0.399.1',
          },
        ],
      },
    },
  },
  intellij_agent_mode_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable agent mode in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
        ] + apply_return_value(
          agents_rubrik_allowlist,
          '0.170.0'
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '0.226.1',
          },
        ] + apply_return_value(
          agents_wave2_latest +
          agents_wave3_latest +
          agents_wave4_latest +
          agents_intellij,
          '0.157.0'
        ) + [
          {
            return_value: '0.170.0',
          },
        ],
      },
    },
  },
  api_proxy_blacklisted_checkpoint_ids: {
    sync: true,
    description: 'Incident-10317 - Comma separated list of checkpoint ids that should be blacklisted from the api proxy',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'd1',
            ],
            return_value: '4aa2a5395a151c6055fbf7d6ddbd208a1785b0335f2c903c0745f7e95f01ac3f',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  agent_continuation_latency_injection: {
    sync: true,
    description: 'Number of seconds to delay AGENT_CHAT requests delivering tool results',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.2,
          },
          {
            return_value: 0.2,
          },
        ],
      },
    },
  },
  vscode_deprecated_version: {
    sync: true,
    description: 'Defines the version of the VS Code client at or below which we will report it deprecated.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_deprecated_version: {
    sync: true,
    description: 'Defines the version of the IntelliJ client at or below which we will report it deprecated.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_blocked_versions: {
    sync: true,
    description: 'Comma separated list of blocked IntelliJ versions',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_syncing_progress_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to show syncing progress in chat.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  elo_model_configuration: {
    sync: true,
    description: 'Configuration of models for ELO comparisons in AiTutors',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'staging-shard-0',
            ],
            return_value: {
              // highPriorityModels can be either:
              // 1. A flat list of models for random pair selection (battle royale)
              // 2. A list of pairs ([[model1, model2], [model3, model4]]) for specific comparisons
              highPriorityModels: [
                'claude-sonnet-v18-c4-p2-chat',
                'claude-sonnet-v17-balanced-c4-p2-chat',
              ],
              regularBattleModels: [
                'claude-sonnet-v17-balanced-c4-p2-chat',
                'claude-sonnet-16k-v16-c4-p2-chat',
                'claude-sonnet-16k-v16r2-chat',
                'claude-sonnet-3-5-16k-v11-4-chat',
                'claude-sonnet-16k-v15-1r2-chat',
                'claude-sonnet-16k-v15-1nor2-chat',
                'claude-sonnet-16k-v16nor2-chat',
                'claude-sonnet-16k-v16r2-chat',
              ],
              highPriorityThreshold: 0,
            },
          },
          {
            return_value: {},
          },
        ],
      },
    },
  },
  vscode_chat_stable_prefix_truncation_min_version: {
    sync: true,
    description: 'Whether to use the chat history truncation yielding more stable prefix to protect caching',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.402.0',
          },
        ],
      },
    },
  },
  memories_params: {
    sync: true,
    description: 'All parameters of the memories system, including prompts',
    envs: {
      production: {
        rules: [
          // Memory retrieval experiment 1: default version (1/3 of staging users)
          {
            user_uuid: [
              '03bb01ee-03ee-481c-a8c2-abf49f284eff',
              '043d1d91-07d1-42c2-b946-d6fe32461f77',
              '059afccc-e8c4-4a82-933d-30bdd0b8b251',
              '05ed906c-11b8-4340-b9c8-d1d4ff64a1d1',
              '0919e511-5a3c-4e72-9b05-90454dc1ba60',
              '099d773d-7c96-49c5-b041-176a5bb41f06',
              '0a577a07-544b-4fdc-b1a9-86570e8964c7',
              '0a62a3e0-41ab-4385-ac61-e4e16df5a10c',
              '0add9c4d-4319-4d77-ae09-15077d7a7a24',
              '0cdb3235-b613-4494-9bb6-93007719007a',
              '0d70c55a-5f29-44bd-ad70-f731769ccd8f',
              '10383445-7c3d-4cdf-8b69-33ebb845d935',
              '15d6de72-fbac-484d-99ac-90d978a76b3f',
              '17bbbf50-c79b-4b98-a6d0-764d9088c70b',
              '1dc0e2aa-2c56-46dc-abbd-e05e04389502',
              '1f6bf40a-ae41-4053-8745-7f0d759ffb23',
              '20d7adde-0817-4849-b82a-49a0d3f63390',
              '214ac23c-b6ff-45ad-ab93-413ed38b708c',
              '21f2bbd6-6516-43bd-a383-60ecff7fbf9c',
              '27c404dd-79c6-4ae0-9197-7bd8f4869ed3',
              '2827bfbf-6ff3-4648-8b7e-274e1fd18fcc',
              '29a39102-abc9-4cb2-89e4-695d5fad69d0',
              '2d81d050-d975-4a6e-b4c5-a6f99ef27a27',
              '2e31890b-d01c-4376-b8a0-7cce8d5ef9ec',
              '3048cf52-fca7-4963-89f8-7f4c2c9e2688',
              '309e443f-c420-453a-ac05-9a2a4f963cec',
              '31a2ecad-7e02-456b-bd9b-642cc52d9886',
              '36f6918b-5d35-4876-9f1b-12429e111e87',
              '37f1e165-2a4a-4a1d-a513-bbd879f2d5f6',
              '3c50301a-02b1-4401-a9fe-a11589f56470',
              '3c821893-f761-48fa-9780-b96c9a84ea18',
              '40bd4949-5a2a-45af-bf81-0bea0e0e7211',
              '42b50230-a238-4dfc-86e6-c71278aa3c82',
              '45aeda54-786e-4a75-bd8a-95322a13f165',
            ],
            namespace: [
              'staging-shard-0',
            ],
            return_value: shared_memory_prompts + shared_memory_config + {
              enable_initial_orientation: true,
              memory_retrieval_version: 'default',
            },
          },
          // Memory retrieval experiment 2: v1_success version (1/3 of staging users)
          {
            user_uuid: [
              '45b9f89b-b3f5-4fb1-a214-c5573280002e',
              '467ed431-e49b-4ed7-be2c-f3362f5cd331',
              '47053bba-9fe3-4586-96a2-abf2eb10c731',
              '49bf3c10-d304-4dba-b029-f5767f986d24',
              '4a23d687-613a-4ce3-a6f2-e0549871209e',
              '4dc1ff86-397a-4dc5-9e22-92209acd4ad9',
              '518e5df7-3fb1-4798-b26c-54d5abab5eb3',
              '565f3e61-3e32-4e01-8be3-6bf9df98c130',
              '56db3de3-b409-4dd6-98ce-0105b8703c06',
              '57972414-fa3f-4651-8b5a-0992b3fc844a',
              '57a5b6c4-4e0e-4c20-909f-6ddc13f665db',
              '5a6d7d71-7cf7-4e02-921b-6a19fd737f93',
              '5f72eb8c-2f74-4e29-9cb7-2e8b3c05fdb4',
              '647f1ec2-2af4-4578-a936-adbd0cb3b5da',
              '652315e6-37f0-4f89-96be-b13247792d24',
              '6a0b0f26-5690-4da4-86b1-7d1324fab320',
              '6b92a951-650c-414e-95c1-b1a14f62f6ba',
              '6cb711da-8b55-49fd-84cd-1f8f356a5a2b',
              '6e93f5b9-f5fc-4f02-926b-ca9795816b54',
              '6ff1887a-3b64-4203-a821-06fe4128412b',
              '71b284e2-e7e2-4f08-a6c0-a9e2d1564b13',
              '750fecb1-6d8c-4024-86cb-47b4d61b82f2',
              '77a5f6b8-d82a-4334-a3fb-23e9f4d37790',
              '783bf9c6-e331-4e58-868d-38929168f02f',
              '7bb60a3e-b1d7-4e4b-99a3-f96bedcd05d5',
              '7bf0768c-767e-419c-8585-755ee1e3c316',
              '80709060-dfc7-43ed-b9f9-700064aa7e80',
              '854e9702-8e9e-4f1f-90a0-06b0f2c29faf',
              '8a75f9c5-0138-403d-9ace-11b55dc4952c',
              '8d1e2739-a6f7-45dd-8201-b3ba9572a5f8',
              '907d41b1-2f10-45de-bea1-35cff9de5384',
              '96cb82e0-d8dd-4cd4-92fd-6c30506e12e0',
              '9a2873f2-b94c-4a6b-bcd4-6bc56de90390',
              '9d3f677f-5a8c-41dd-b1b8-74b14aa639df',
            ],
            namespace: [
              'staging-shard-0',
            ],
            return_value: shared_memory_prompts + shared_memory_config + {
              enable_initial_orientation: true,
              memory_retrieval_version: 'v1_success',
            },
          },
          // Memory retrieval experiment 3: v2_scope version (1/3 of staging users)
          {
            user_uuid: [
              '9dc80b09-d7d9-41d7-be88-5ed87328b64f',
              'a3c54f3a-586e-4f55-bb0e-b32892c2f397',
              'a50f2e92-4487-4bf0-9989-785a9efa0437',
              'a8f0fcab-fded-4c6e-be54-0e6db4b42b19',
              'aade4100-e10a-4e61-b23b-b8e8bf9a5350',
              'adedb7e3-ec36-416e-84ba-0ef083e53876',
              'c1e5360e-ccd2-4b64-b1ae-80ab0e6c383a',
              'c209298d-47af-475b-a8a2-b4acf6f86a9e',
              'c338f503-b057-466e-bcf5-8bdea3ee9c97',
              'c7143873-0068-4ead-9909-67cf8f6b9e89',
              'ca5bbdb3-eefa-4f7c-80a4-9ff2c98673aa',
              'cac7e8cc-e30f-4189-a5d3-3bbff55e499d',
              'd0eb39e3-249a-4b78-97f5-753bbffb67eb',
              'd52e1042-0c83-4489-9cf8-f398a744eab9',
              'd8809956-978e-4949-b05d-8c91dd1b4d11',
              'd8f0cda7-e3de-4974-9461-dae77b409e75',
              'd978e6a2-f95b-46c1-989f-31517925be91',
              'db07701c-daf8-45e7-9204-6b9c67b9387a',
              'db134865-99ee-4095-a86b-70a885b7a144',
              'dc631d96-ebdc-40d4-8dec-e528d53702ab',
              'dc81f124-9b6a-49fd-a76e-c63db31c9d38',
              'dd096ae8-cbb9-4aca-8499-c37014b0c4a1',
              'de4e6833-0b2c-4a14-852b-e09bc699f3a5',
              'de8f08ea-4ad7-4449-8f66-b0ab16a3628b',
              'df38a660-a7a4-4e11-9d0e-02e4ba438433',
              'e420e24b-26c3-44b7-885a-d364630ff88e',
              'e5f0ce23-f235-490c-95ea-c1fd40c34644',
              'ea44baaa-dca9-47b8-8d11-4ffcdea0e0ee',
              'edc78ea4-986d-4233-9a55-6cc51e2eb0d3',
              'ee04dad3-4f12-4b74-bfaa-29a6c3d5eeeb',
              'f2323e2b-ab3f-4928-8235-d57f82ddaeed',
              'f5f1f77d-98f3-4e49-87aa-4d1102c8121e',
              'fa46f79c-1cb0-4c4e-9e4b-becc2b589e7b',
            ],
            namespace: [
              'staging-shard-0',
            ],
            return_value: shared_memory_prompts + shared_memory_config + {
              enable_initial_orientation: true,
              memory_retrieval_version: 'v2_scope',
            },
          },
          {
            namespace: [
              'aitutor-turing',
            ],
            return_value: shared_memory_prompts + shared_memory_config + {
              enable_initial_orientation: true,
              memory_retrieval_version: 'v1_success',
            },
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: shared_memory_prompts + shared_memory_config + {
              enable_initial_orientation: true,
              memory_retrieval_version: 'v2_scope',
            },
          },
          {
            return_value: shared_memory_prompts + shared_memory_config + {
              enable_initial_orientation: false,
            },
          },
        ],
      },
    },
  },
  memory_filter: {
    sync: true,
    description: 'Regex pattern for filtering classify_and_distill requests in the backend.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: '.*',
          },
          {
            return_value: std.format(
              '\\b(%s)\\b',
              std.join('|', [
                '(we|this|you|it|these) should',
                '(i|we) want',
                '(want|need) you',
                'we have',
                'please (do|make|perform)',
                'maintain existing',
                'following specific',
              ])
            ),
          },
        ],
      },
    },
  },  // Note! This flag supports more than just the /smart-paste-stream endpoint; backend
  // services may rely on a smart paste model being available in the namespace.
  intellij_ask_for_sync_permission_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to ask for sync permission.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_background_agents_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable launching background agents.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            namespace: self_serve_namespaces,
            return_value: '0.472.1',
          },
          {
            tenant_name: remote_agents_enterprise_tenants,
            return_value: '0.472.1',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_background_agents_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable launching background agents.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_virtualized_message_list_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable virtualized message list.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_rich_checkpoint_info_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable rich checkpoint info.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.393.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  next_edit_inferer_multiplex: {
    sync: true,
    description: 'Enable multiplexing over inference hosts for next edit models',
    envs: {
      production: {
        rules: [
          {
            return_value: '{"default": 0, "gsc": 1.0}',
          },
        ],
      },
    },
  },
  next_edit_use_stream_mux: {
    sync: true,
    description: 'Enable stream muxing for next edit',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_enable_model_name: {
    sync: true,
    description: 'If false, the model name parameter sent to api proxy will be ignored.',
    envs: {
      production: {
        rules: [
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: true,
          },
          {
            env: ['STAGING'],
            return_value: true,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_async_ops_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the auth central async ops worker will process messages from the dead letter queue',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_async_ops_log_dead_letters: {
    sync: true,
    description: 'If true, the auth central async ops worker will log dead letter queue messages',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_user_tier_change: {
    sync: true,
    description: 'If true, the auth central user tier change RPC & worker will be enabled',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_team_management_enabled: {
    sync: true,
    description: 'If true, the auth central team management RPCs will be enabled on the backend',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_invite_users_to_tenant_plus_allowed: {
    sync: true,
    description: 'If true, the auth central service will allow inviting users to a tenant with a + in their email',
    envs: {
      production: {
        rules: [
          {
            env: ['PROD'],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  // intellij remember tool min version. return 0.161 for staging and shv and false for everyoine else
  intellij_remember_tool_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to use the remember tool feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.161.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  agents_use_ide_state_in_prompt: {
    sync: true,
    description: 'If true, render the IDE state in the prompt. This is temporary while we roll out the feature. Shout at arun@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agents_add_supervisor_prompt_every_turn: {
    sync: true,
    description: 'If true, add the supervisor prompt every turn. This prompt is used to reminder the agent of important instructions. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agents_add_supervisor_prompt_to_prefill_every_turn: {
    sync: true,
    description: 'If true, add the supervisor prompt to model response prefill on every turn. This prompt is used to reminder the agent of important instructions. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agents_add_system_prompt_to_prefill_every_turn: {
    sync: true,
    description: 'If true, add the system prompt to model response prefill on every turn. This can potentially make the model follow the system prompt more closely. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agents_add_date_to_system_prompt: {
    sync: true,
    description: 'If true, add the current date to the system prompt. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agents_use_edit_events_in_prompt: {
    sync: true,
    description: 'If true, add the edit events to the prompt. This is temporary while we roll out the feature. Shout at jeff@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agents_edit_events_version: {
    sync: true,
    description: 'The version of the edit events prompt formatter to use.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'v2',
          },
          {
            return_value: 'v2',
          },
        ],
      },
    },
  },
  agents_save_file_partial_tool_use: {
    sync: true,
    description: 'If true, return partial tool use for the save-file command. This is temporary while we roll out the feature. Shout at jeff@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },

  intellij_enable_homespun_gitignore: {
    sync: true,
    description: 'If true, the intellij client will use a reimplemented gitignore parser, rather than nl.basjes.gitignore.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            user_uuid: [
              '11202063-3a59-434a-9158-9a0d63f43d2b',
              '17c06c71-5375-4b1d-84ee-8c98cc11b059',
              '38118a4f-7bdc-4139-878f-f634d4dfe71a',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
              'empower',
              'egnyte',
              'i0-vanguard0',
              'intuit',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  // intellij enable workspace guidelines feature flag
  enable_supabase_service: {
    sync: true,
    description: 'If true, the Supabase service is enabled for the user. This controls access to the Supabase Management API.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  misuse_monitor_dry_run: {
    sync: true,
    description: 'If true, the misuse monitor will run in dry run mode, which means it will log potential misuse but not take any action. This is useful for testing and validating the misuse detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_duplication_dry_run: {
    sync: true,
    description: 'If true, the free trial duplication monitor will run in dry run mode, which means it will log potential free trial duplication but not take any action. This is useful for testing and validating the free trial duplication detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_fv_session_duplication_dry_run: {
    sync: true,
    description: 'If true, the free trial feature vector session duplication monitor will run in dry run mode, which means it will log potential free trial duplication but not take any action. This is useful for testing and validating the free trial duplication detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_forgiveness_dry_run: {
    sync: true,
    description: 'If true, the free trial forgiveness monitor will run in dry run mode, which means it will log potential free trial forgiveness but not take any action. This is useful for testing and validating the free trial forgiveness logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  free_trial_recent_user_duplication_dry_run: {
    sync: true,
    description: 'If true, the misuse monitor job checking recent users for free trial abuse will run in dry run mode, which means it will log potential free trial abuse but not take any action. This is useful for testing and validating the logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  disposable_email_domain_dry_run: {
    sync: true,
    description: 'If true, the disposable email domain monitor will run in dry run mode, which means it will log potential disposable email domain use but not take any action. This is useful for testing and validating the disposable email domain detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_feature_vector_duplication_v2_dry_run: {
    sync: true,
    description: 'If true, the free trial feature vector duplication monitor V2 will run in dry run mode, which means it will log potential free trial duplication but not take any action. This V2 version includes improved field name logging and enhanced fraud detection logic.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  free_trial_verisoul_dry_run: {
    sync: true,
    description: 'If true, the free trial verisoul report checking monitor will run in dry run mode, which means it will log potential free trial abuse but not take any action. This is useful for testing and validating the verisoul report based detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  community_abuse_suspension_enabled: {
    sync: true,
    description: 'If true, the community abuse suspensions will be issued, otherwise, the potential suspensions will be counted only. This is useful for testing and validating the community abuse detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  free_trial_conversation_duplication_dry_run: {
    sync: true,
    description: 'If true, the free trial conversation duplication monitor will run in dry run mode, which means it will log potential free trial duplication but not take any action.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_throttle_block_duration_ms: {
    sync: true,
    description: "The duration (in ms) to block for when we're throtting a user in api proxy.",
    envs: {
      production: {
        rules: [
          {
            return_value: 30000,
          },
        ],
      },
    },
  },

  chat_server_max_cjk_char_count: {
    sync: true,
    description: 'The maximum number of CJK (Chinese, Japanese, Korean) characters allowed in a chat request. If the count exceeds this value, the request will be blocked (if blocking is enabled).',
    envs: {
      production: {
        rules: [
          {
            return_value: 2000,
          },
        ],
      },
    },
  },
  chat_server_block_high_cjk_count: {
    sync: true,
    description: 'If true, requests with a high count of CJK characters (exceeding chat_server_max_cjk_char_count) will be blocked. If false, these requests will only be logged but not blocked.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_suspicious_max_ips_per_user_hour: {
    sync: true,
    description: 'Maximum number of IP addresses allowed per user ID in single api proxy instance within the last hour. If a user exceeds this number, their requests will be marked as suspicious. Set to 0 to disable this check.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  auth_central_enable_stripe_event_processor: {
    sync: true,
    description: 'If true, the auth central service will initialize and run the Stripe event processor. Set to false in dev environments to make Stripe webhook optional.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_stripe_event_processor_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the Stripe event processor will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_enable_billing_event_processor: {
    sync: true,
    description: 'If true, the auth central service will initialize and run the Billing event processor. \n    Always set to false in dev environments to make Billing webhook optional.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_billing_event_processor_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the Billing event processor will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_use_user_email_index: {
    sync: true,
    description: 'If true, the auth central service will use the user email index to get user by email. If false, user table will be scanned for matching users.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_enable_token_hash_user_id_index: {
    sync: true,
    description: 'If true, the auth central service will use the token hash user id index to get token hashes by user id. If false, token hash table will be scanned for matching token hashes.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_anthropic_vertexai_load_balance_europe_rate: {
    sync: true,
    description: 'The percentage of requests to route to the Europe region for Anthropic VertexAI client.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.2,
          },
          {
            return_value: 0.27,
          },
        ],
      },
    },
  },
  chat_anthropic_vertexai_load_balance_asia_rate: {
    sync: true,
    description: 'The percentage of requests to route to the Asia region for Anthropic VertexAI client.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.2,
          },
          {
            return_value: 0.51,
          },
        ],
      },
    },
  },
  chat_anthropic_direct_rate: {
    sync: true,
    description: 'The percentage of requests to route to the Anthropic direct client.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.1,
          },
          {
            return_value: 0.22,
          },
        ],
      },
    },
  },
  chat_anthropic_vertexai_load_balance_models: {
    sync: true,
    description: 'A comma-separated list of models for which to enable load balancing for Anthropic VertexAI client.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-3-7-sonnet@20250219',
          },
        ],
      },
    },
  },
  embeddings_search_checkpoint_ann_indexing_enabled: {
    sync: true,
    description: 'If true, the embeddings search service will trigger ann indexing of checkpoints it sees.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  embeddings_search_use_indexed_checkpoint_cache: {
    sync: true,
    description: 'If true, the embeddings search service will use the indexed checkpoint cache for searches.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  embeddings_search_indexed_checkpoint_quality_sampling_probability: {
    sync: true,
    description: 'Probability of selecting a checkpoint for quality verification. Set to 0.0 to disable',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.0005,
          },
        ],
      },
    },
  },
  team_management: {
    sync: true,
    description: 'If true, the team management page will be enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  team_management_canary_domains: {
    sync: true,
    description: "A comma-separated list of email domains to whitelist for team management canary. Users with email addresses from these domains will have team management enabled, overriding the 'team_management' flag.",
    envs: {
      production: {
        rules: [
          {
            return_value: 'augm.io,turing.com',
          },
        ],
      },
    },
  },
  team_management_subscription_change_blocking_enabled: {
    sync: true,
    description: 'If true, enable blocking of operations when subscription/plan changes are in progress. When disabled, allows operations to proceed even during subscription changes.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  team_management_pending_changes_enabled: {
    sync: true,
    description: 'If true, the pending changes checkout flow will be enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_billing_event_ingestion: {
    sync: true,
    description: 'Whether the event ingestion to Orb is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  workingset_persist_to_bigtable: {
    sync: true,
    description: 'Persist workingset internal state to Bigtable',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_remote_agents: {
    sync: true,
    description: 'If true, remote agents APIs are enabled. Used to roll out remote agents incrementally while it is under development. Note that vscode_background_agents_min_version and intellij_background_agents_min_version need to be set to enable the background agents UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'eu-staging-0',
            ],
            return_value: true,
          },
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            tenant_name: remote_agents_enterprise_tenants,
            return_value: true,
          },
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  remote_agents_scan_interval_seconds: {
    sync: true,
    description: 'The interval in seconds for scanning remote agents in the background cron task. Controls how frequently the system checks for agents that need to be paused or cleaned up.',
    envs: {
      production: {
        rules: [
          {
            return_value: 300,
          },
        ],
      },
    },
  },
  max_remote_agents_per_user: {
    sync: true,
    description: 'The maximum number of remote agents per user (this includes paused agents).',
    envs: {
      production: {
        rules: [
          {
            return_value: 100,
          },
        ],
      },
    },
  },
  max_active_remote_agents_per_user: {
    sync: true,
    description: 'The maximum number of remote agents per user that can be active at a time.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  remote_agents_enable_auto_pause: {
    sync: true,
    description: 'If true, remote agents will be automatically paused after a period of inactivity.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  remote_agents_force_pause_for_suspended_users: {
    sync: true,
    description: 'If true, remote agents will be automatically paused for suspended users, or inactive subscriptions.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  remote_agents_enable_auto_delete: {
    sync: true,
    description: 'If true, remote agents will be automatically deleted after a period of inactivity.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  remote_agents_max_deletions_per_cron_run: {
    sync: true,
    description: 'The maximum number of remote agents that can be deleted in a single cron run.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  remote_agents_deletion_pending_period_hours: {
    sync: true,
    description: 'The number of hours after which a remote agent is considered inactive (user, agent activity) and will be deleted.',
    envs: {
      production: {
        rules: [
          {
            return_value: 48,
          },
        ],
      },
    },
  },
  remote_agents_auto_pause_soft_ttl_minutes: {
    sync: true,
    description: 'The number of minutes after which a remote agent is considered inactive (user, agent, ssh activity) and will be paused.',
    envs: {
      production: {
        rules: [
          {
            return_value: 15,
          },
        ],
      },
    },
  },
  remote_agents_auto_pause_hard_ttl_minutes: {
    sync: true,
    description: 'The number of minutes after which a remote agent is considered inactive (user, agent activity) and will be paused.',  // 24 hours
    envs: {
      production: {
        rules: [
          {
            return_value: 1440,
          },
        ],
      },
    },
  },
  remote_agents_auto_delete_ttl_days: {
    sync: true,
    description: 'The number of days after which a remote agent is considered inactive (user, agent activity) and will be deleted.',
    envs: {
      production: {
        rules: [
          {
            return_value: 30,
          },
        ],
      },
    },
  },
  remote_agents_resume_hint_available_ttl_days: {
    sync: true,
    description: 'The number of days after which a remote agent is not available for a resume hint.',
    envs: {
      production: {
        rules: [
          {
            return_value: 21,
          },
        ],
      },
    },
  },
  agent_edit_tool_min_view_size: {
    sync: true,
    description: 'Minimum number of lines that agent can read from a file. If it tries to read fewer, we expand the range.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  agent_edit_tool_schema_type: {
    sync: true,
    description: 'The schema type of the agent edit tool. Can be StrReplaceEditorToolDefinitionNested or StrReplaceEditorToolDefinitionFlat.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'StrReplaceEditorToolDefinitionFlat',
          },
          {
            return_value: 'StrReplaceEditorToolDefinitionFlat',
          },
        ],
      },
    },
  },
  agent_edit_tool_enable_fuzzy_matching: {
    sync: true,
    description: 'If true, fuzzy matching is enabled in the str-replace-editor-tool. If false, only exact matches are allowed.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_success_message: {
    sync: true,
    description: 'The success message to display when fuzzy matching is used in the str-replace-editor-tool.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'Replacement successful. old_str and new_str were slightly modified to match the original file content.',
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_max_diff: {
    sync: true,
    description: 'Maximum number of differences allowed in fuzzy matching for str-replace-editor-tool',
    envs: {
      production: {
        rules: [
          {
            return_value: 50,
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_max_diff_ratio: {
    sync: true,
    description: 'Maximum ratio of differences to string length allowed in fuzzy matching for str-replace-editor-tool',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.15,
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs: {
    sync: true,
    description: 'Minimum number of consecutive matching symbols required between differences in fuzzy matching',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  agent_save_file_tool_instructions_reminder: {
    sync: true,
    description: 'If true, special `instructions_reminder` field is added to the save-file tool input schema to remind the agent to limit the file content',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'i0',
              'd18',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_edit_tool_instructions_reminder: {
    sync: true,
    description: 'If true, special `instructions_reminder` field is added to the str-replace-editor tool input schema to remind the agent to limit the file content',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_edit_tool_show_result_snippet: {
    sync: true,
    description: 'If true, the agent edit tool will show result snippets after successful edits',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agent_edit_tool_max_lines: {
    sync: true,
    description: 'Maximum number of lines for edit tool instructions reminder',
    envs: {
      production: {
        rules: [
          {
            return_value: 150,
          },
        ],
      },
    },
  },
  auth_enable_team_invitation_email: {
    sync: true,
    description: 'If true, the auth central service will send invitation emails for team management.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  chat_server_blocked_domains: {
    sync: true,
    description: 'Comma-separated list of domains that should be blocked from making requests.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: |||
              deepmails.org
              fage.asia
              falai.online
              fale.asia
              fanno.fun
              fanss.fun
              feiniao.site
              fengchemail0517.asia
              fengchemail.asia
              fengche.site
              fifaa.fun
              figuree.online
              fivee.site
              futuree.fun
              ggapi.dpdns.org
              gggapi.ggff.net
              gmail.pm
              hotanmi.com
              inctart.com
              ision.us
              justdefinition.com
              kccc.tk
              kenzor.me
              loveu.kg
              mail.xans.me
              mailto.plus
              mix27.tokyo
              mkzaso.com
              nuoxuan.tk
              overhell.me
              oz5j.us
              ptct.net
              redaaa.me
              sunanus.me
              swagpapa.com
              temp.now
              tmpmails.com
              voox.eu.org
              xmyy.shop
              xn--m7rx30aitf.site
              xoxome.dpdns.org
              xoxome.top
              yuanyoupush.com
              dcpa.net
            |||,
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  memories_model: {
    sync: true,
    description: 'Model used in Memories',
    envs: {
      production: {
        rules: [
          {
            return_value: 'gemini-2-flash-001-simple-port',
          },
        ],
      },
    },
  },
  memories_fallback_model: {
    sync: true,
    description: 'Fallback model used in Memories',
    envs: {
      production: {
        rules: [
          {
            return_value: 'gemini-2-flash-001-simple-port',
          },
        ],
      },
    },
  },
  memories_compression_model: {
    sync: true,
    description: 'Model used for Memories compression',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  memories_compression_fallback_model: {
    sync: true,
    description: 'Fallback model used for Memories compression',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  orientation_model: {
    sync: true,
    description: 'Model used in Orientation',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  orientation_fallback_model: {
    sync: true,
    description: 'Fallback model used in Orientation',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  api_proxy_chat_agent_daily_limit_enabled: {
    sync: true,
    description: 'If true, the chat agent will have a daily request limit. When a user exceeds this limit, they will receive a message indicating they have reached their limit for the day.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_chat_agent_daily_limit_message: {
    sync: true,
    description: 'The message shown to users when they reach their daily chat agent request limit.',
    envs: {
      production: {
        rules: [
          {
            return_value: "We're currently experiencing high system volume. To ensure service quality for all users, we've temporarily paused your Agent access. We apologize for the inconvenience. Agent access will resume at midnight UTC. [Fair Use Policy](http://www.augmentcode.com/terms-of-service/fair-use)",
          },
        ],
      },
    },
  },
  api_proxy_chat_agent_daily_limit_max_requests: {
    sync: true,
    description: 'The maximum number of chat agent requests a user can make per day. This limit resets at midnight UTC.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 1000,
          },
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_chat_remote_agent_daily_limit_enabled: {
    sync: true,
    description: 'If true, the chat agent will have a daily request limit. When a user exceeds this limit, they will receive a message indicating they have reached their limit for the day.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_chat_remote_agent_daily_limit_message: {
    sync: true,
    description: 'The message shown to users when they reach their daily chat agent request limit.',
    envs: {
      production: {
        rules: [
          {
            return_value: "We're currently experiencing high system volume. To ensure service quality for all users, we've temporarily paused your Agent access. We apologize for the inconvenience. Agent access will resume at midnight UTC. [Fair Use Policy](http://www.augmentcode.com/terms-of-service/fair-use)",
          },
        ],
      },
    },
  },
  api_proxy_chat_remote_agent_daily_limit_max_requests: {
    sync: true,
    description: 'The maximum number of chat remote agent requests a user can make per day. This limit resets at midnight UTC.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 1000,
          },
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  misuse_monitor_banned_users_gcs_path: {
    sync: true,
    description: 'Relative path under ban_list/ in the GCS bucket for a CSV file of banned users. The CSV should have columns: opaque_user_id, email, tenant_id. Uses augment-data bucket for prod/staging and augment-data-dev for dev/test.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'malicious_ip_20250426.csv',
          },
        ],
      },
    },
  },
  auth_central_enable_feature_gating_info: {
    sync: true,
    description: 'If true, enables populating featureGatingInfo in GetTokenInfo responses for backend-driven feature control.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_use_feature_gating: {
    sync: true,
    description: 'If true, api proxy uses feature gating info instead of subscription fields for feature control.',
    envs: {
      production: {
        rules: [
          {
            env: ['STAGING'],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },

  test_client_segmentation_flag: {
    sync: true,
    description: 'A test flag for new client segmentation of flags',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'intellij',
            min_client_version: '1.70.0',
            max_client_version: '1.80.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'vscode',
            min_client_version: '1.0.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: [
              'vim',
              'emacs',
              'neovim',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_personalities_min_version: {
    sync: true,
    description: 'Sets the minimum version number of VSCode to enable personalities feature. Empty means "disable for all versions". "0.0.0" means "enable for all versions"',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  content_manager_rate_limit_by_user_id: {
    sync: true,
    description: 'If true, the content manager will rate limit by user id instead of tenant id.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  websearch_tool_safety: {
    sync: true,
    description: 'The safety of the web search tool. True means the tool is safe, False means the tool is unsafe.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'maxar',
              'maxar-cmk',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  remote_agent_chat_history_polling_interval_ms: {
    sync: true,
    description: 'The polling interval to use for polling the remote agent chat history',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  remote_agent_list_polling_interval_ms: {
    sync: true,
    description: 'The polling interval to use for polling the remote agent list',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  request_insight_find_missing_enabled: {
    sync: true,
    description: 'If true, the request insight find missing subscriber is enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  checkpoint_indexer_ack_deadline_s: {
    sync: true,
    description: 'Bigtable ack deadline (seconds) to set when extending the deadline during long running operations; 0 to never extend.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60,
          },
        ],
      },
    },
  },
  vscode_generate_commit_message_min_version: {
    sync: true,
    description: 'The minimum version of the VS Code client to enable generate commit message',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  enable_prompt_enhancer: {
    sync: true,
    description: 'If true, the prompt enhancer button is visible.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  memories_text_editor_enabled: {
    sync: true,
    description: 'If true, the memories text editor is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            client: 'vscode',
            min_client_version: '0.492.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_model_registry: {
    sync: true,
    description: 'If true, the model registry will be enabled and used to populate the model selection dropdown in the chat UI.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              'b6e0e364-7539-4797-bb30-a1d3f0524e1a',  // Shao personal
              '11e6c7c6-f500-4b95-9105-f27756abd514',  // Justin Xu personal
            ],
            client: 'vscode',
            min_client_version: '0.524.0',
            return_value: true,
          },
          {
            user_uuid: [
              'b6e0e364-7539-4797-bb30-a1d3f0524e1a',  // Shao personal
              '11e6c7c6-f500-4b95-9105-f27756abd514',  // Justin Xu personal
            ],
            client: 'intellij',
            min_client_version: '0.265.2',
            return_value: true,
          },

          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'shv',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  model_registry: {
    sync: true,
    description: 'A mapping of model IDs to display names. This is used to populate the model selection dropdown in the chat UI.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              'b6e0e364-7539-4797-bb30-a1d3f0524e1a',  // Shao personal
              '11e6c7c6-f500-4b95-9105-f27756abd514',  // Justin Xu personal
            ],
            // Redact model name for now in case it leaks
            return_value: |||
              {
                "Claude Sonnet 4": "claude-sonnet-4-0-200k-v8-c4-p2-agent",
                "Model X": "gpt5-reasoning-medium-200k-v7-c4-p2-agent"
              }
            |||,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: |||
              {
                "Claude Sonnet 4": "claude-sonnet-4-0-200k-v9-c4-p2-agent",
                "Memories v1": "claude-sonnet-memories-v1-c4-p2-agent",
                "Claude Opus 4": "claude-opus-4-0-200k-v7-c4-p2-agent",
                "Claude Opus 4.1": "claude-opus-4-1-200k-v8-c4-p2-agent",
                "Gemini 2.5 Pro": "gemini2-5-pro-200k-v7-c4-p2-agent",
                "Grok 4": "grok4-200k-v7-c4-p2-agent",
                "GPT-5 medium": "gpt5-reasoning-medium-200k-v7-c4-p2-agent",
                "GPT-5 low": "gpt5-reasoning-200k-v7-c4-p2-agent",
                "GPT-5 high": "gpt5-reasoning-high-200k-v7-c4-p2-agent",
                "Kimi K2": "kimi-k2-200k-v7-c4-p2-agent",
                "Qwen3-Coder": "qwen3-coder-200k-v7-c4-p2-agent"
              }
            |||,
          },
          {
            tenant_name: [
              'shv',
            ],
            return_value: |||
              {
                "Claude Opus 4": "claude-opus-4-0-200k-v7-c4-p2-agent",
                "Gemini 2.5 Pro": "gemini2-5-pro-200k-v3-2-c4-p2-agent"
              }
            |||,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: |||
              {
                "Model X": "claude-sonnet-4-0-200k-v8-c4-p2-agent",
                "Model Z": "gpt5-reasoning-medium-200k-v7-c4-p2-agent",
                "Model A": "gpt5-reasoning-high-200k-v7-c4-p2-agent"
              }
            |||,
          },
          {
            return_value: '{}',
          },
        ],
      },
    },
  },
  model_info_registry: {
    sync: true,
    description: 'A mapping of model IDs to properties (displayName, shortName, description, disabled, disabled_reason). Used by CLI for model selection.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              'b6e0e364-7539-4797-bb30-a1d3f0524e1a',  // Shao personal
              '11e6c7c6-f500-4b95-9105-f27756abd514',  // Justin Xu personal
            ],
            // Redact model name for now in case it leaks
            return_value: |||
              {
                "claude-sonnet-4-0-200k-v8-c4-p2-agent": {
                  "displayName": "Claude Sonnet 4",
                  "shortName": "sonnet4",
                  "description": "Anthropic Claude Sonnet 4, 200k context",
                  "disabled": false
                },
                "gpt5-reasoning-medium-200k-v7-c4-p2-agent": {
                  "displayName": "Model X",
                  "shortName": "gpt5",
                  "description": "OpenAI GPT-5 reasoning (medium), 200k context",
                  "disabled": false
                }
              }
            |||,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: |||
              {
                "claude-sonnet-4-0-200k-v9-c4-p2-agent": {
                  "displayName": "Claude Sonnet 4",
                  "shortName": "sonnet4",
                  "description": "Anthropic Claude Sonnet 4, 200k context",
                  "disabled": false
                },
                "claude-sonnet-memories-v1-c4-p2-agent": {
                  "displayName": "Memories v1",
                  "shortName": "memories-v1",
                  "description": "Claude Sonnet with Memories v1",
                  "disabled": true,
                  "disabled_reason": "Memory retrieval deprioritized"
                },
                "claude-opus-4-0-200k-v7-c4-p2-agent": {
                  "displayName": "Claude Opus 4",
                  "shortName": "opus4",
                  "description": "Anthropic Claude Opus 4, 200k context",
                  "disabled": false
                },
                "claude-opus-4-1-200k-v8-c4-p2-agent": {
                  "displayName": "Claude Opus 4.1",
                  "shortName": "opus4.1",
                  "description": "Anthropic Claude Opus 4.1, 200k context",
                  "disabled": false
                },
                "gemini2-5-pro-200k-v7-c4-p2-agent": {
                  "displayName": "Gemini 2.5 Pro",
                  "shortName": "gemini25-pro",
                  "description": "Google Gemini 2.5 Pro, 200k context",
                  "disabled": false
                },
                "grok4-200k-v7-c4-p2-agent": {
                  "displayName": "Grok 4",
                  "shortName": "grok4",
                  "description": "Grok 4, 200k context",
                  "disabled": false
                },
                "gpt5-reasoning-medium-200k-v7-c4-p2-agent": {
                  "displayName": "GPT-5 medium",
                  "shortName": "gpt5-med",
                  "description": "OpenAI GPT-5 reasoning (medium), 200k context",
                  "disabled": false
                },
                "gpt5-reasoning-200k-v7-c4-p2-agent": {
                  "displayName": "GPT-5 low",
                  "shortName": "gpt5-low",
                  "description": "OpenAI GPT-5 reasoning (low), 200k context",
                  "disabled": false
                },
                "gpt5-reasoning-high-200k-v7-c4-p2-agent": {
                  "displayName": "GPT-5 high",
                  "shortName": "gpt5-high",
                  "description": "OpenAI GPT-5 reasoning (high), 200k context",
                  "disabled": false
                },
                "kimi-k2-200k-v7-c4-p2-agent": {
                  "displayName": "Kimi K2",
                  "shortName": "kimi-k2",
                  "description": "Kimi K2, 200k context",
                  "disabled": false
                },
                "qwen3-coder-200k-v7-c4-p2-agent": {
                  "displayName": "Qwen3-Coder",
                  "shortName": "qwen3-coder",
                  "description": "Qwen3-Coder 480B-A35B, 200k context",
                  "disabled": false
                }
              }
            |||,
          },
          {
            tenant_name: [
              'shv',
            ],
            return_value: |||
              {
                "claude-opus-4-0-200k-v7-c4-p2-agent": {
                  "displayName": "Claude Opus 4",
                  "shortName": "opus4",
                  "description": "Anthropic Claude Opus 4, 200k context",
                  "disabled": false
                },
                "gemini2-5-pro-200k-v3-2-c4-p2-agent": {
                  "displayName": "Gemini 2.5 Pro",
                  "shortName": "gemini25-pro",
                  "description": "Google Gemini 2.5 Pro, 200k context",
                  "disabled": false
                }
              }
            |||,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: |||
              {
                "claude-sonnet-4-0-200k-v7-c4-p2-agent": {
                  "displayName": "Model X",
                  "shortName": "model-x",
                  "description": "Staging model X",
                  "disabled": false
                },
                "gpt5-reasoning-medium-200k-v7-c4-p2-agent": {
                  "displayName": "Model Z",
                  "shortName": "model-z",
                  "description": "Staging model Z",
                  "disabled": false
                },
                "gpt5-reasoning-high-200k-v7-c4-p2-agent": {
                  "displayName": "Model A",
                  "shortName": "model-a",
                  "description": "Staging model A",
                  "disabled": false
                }
              }
            |||,
          },
          {
            return_value: '{}',
          },
        ],
      },
    },
  },
  intellij_prompt_enhancer_enabled: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable the prompt enhancer button.',
    envs: {
      production: {
        rules: [
          {
            min_client_version: '0.207.0',
            client: 'intellij',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_enable_webview_performance_monitoring: {
    sync: true,
    description: 'If true, enable performance monitoring in webviews for the IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'dogfood-shard',
              'pollyex',
              'silanano',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_truncate_at_parenthesis: {
    sync: true,
    description: 'If true, the completion host will truncate completions at open parentheses.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              'bbac3382-250e-47e2-99ec-17065ee040c1',  // pranay's dogfood account
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  truncate_at_parenthesis_randomize: {
    sync: true,
    description: 'If true, the completion host will 50% of the time truncate completions at open parentheses IF the enable_truncate_at_parenthesis flag is also true.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  open_file_manager_v2_enabled: {
    sync: true,
    description: 'If true, the open file manager v2 is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'i0',
              'i1',
            ],
            min_client_version: '0.498.0',
            client: 'vscode',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            min_client_version: '0.498.0',
            client: 'vscode',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  publish_user_session_events: {
    sync: true,
    description: 'If true, user session start and end events will be published to Request Insight.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_enable_sentry: {
    sync: true,
    description: 'If true, enable Sentry error reporting in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'i0-vanguard1',
              'i0-vanguard2',
              'i0-vanguard3',
              'i0-vanguard4',
              'i0-vanguard5',
              'i0-vanguard6',
              'i0-vanguard7',
              'i1-vanguard0',
              'i1-vanguard1',
              'i1-vanguard2',
              'i1-vanguard3',
              'i1-vanguard4',
              'i1-vanguard5',
              'i1-vanguard6',
              'i1-vanguard7',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'pollyex',
              'silanano',
            ],
            return_value: true,
          },

          {
            // Enable Sentry for all IntelliJ users by default
            return_value: true,
          },
        ],
      },
    },
  },
  intellij_webview_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for webview errors in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.1,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 0.01,
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'i0-vanguard1',
              'i0-vanguard2',
              'i0-vanguard3',
              'i0-vanguard4',
              'i0-vanguard5',
              'i0-vanguard6',
              'i0-vanguard7',
              'i1-vanguard0',
              'i1-vanguard1',
              'i1-vanguard2',
              'i1-vanguard3',
              'i1-vanguard4',
              'i1-vanguard5',
              'i1-vanguard6',
              'i1-vanguard7',
            ],
            return_value: 0.1,
          },
          {
            return_value: 0.1,
          },
        ],
      },
    },
  },
  beachhead_enable_sentry: {
    sync: true,
    description: 'Enable Sentry error reporting in Beachhead service.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  beachhead_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for errors in Beachhead service.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 1.0,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  beachhead_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for traces in Beachhead service. Disabled by default since we send all beachhead traces to GCP anyway.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  intellij_plugin_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for plugin errors in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 1.0,
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'i0-vanguard1',
              'i0-vanguard2',
              'i0-vanguard3',
              'i0-vanguard4',
              'i0-vanguard5',
              'i0-vanguard6',
              'i0-vanguard7',
              'i1-vanguard0',
              'i1-vanguard1',
              'i1-vanguard2',
              'i1-vanguard3',
              'i1-vanguard4',
              'i1-vanguard5',
              'i1-vanguard6',
              'i1-vanguard7',
            ],
            return_value: 0.1,
          },

          {
            return_value: 0.1,
          },
        ],
      },
    },
  },
  intellij_webview_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for webview traces in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 0.5,
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'i0-vanguard1',
              'i0-vanguard2',
              'i0-vanguard3',
              'i0-vanguard4',
              'i0-vanguard5',
              'i0-vanguard6',
              'i0-vanguard7',
              'i1-vanguard0',
              'i1-vanguard1',
              'i1-vanguard2',
              'i1-vanguard3',
              'i1-vanguard4',
              'i1-vanguard5',
              'i1-vanguard6',
              'i1-vanguard7',
            ],
            return_value: 0.01,
          },
          {
            return_value: 0.01,
          },
        ],
      },
    },
  },
  intellij_plugin_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for plugin traces in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 0.5,
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'i0-vanguard1',
              'i0-vanguard2',
              'i0-vanguard3',
              'i0-vanguard4',
              'i0-vanguard5',
              'i0-vanguard6',
              'i0-vanguard7',
              'i1-vanguard0',
              'i1-vanguard1',
              'i1-vanguard2',
              'i1-vanguard3',
              'i1-vanguard4',
              'i1-vanguard5',
              'i1-vanguard6',
              'i1-vanguard7',
            ],
            return_value: 0.01,
          },

          {
            return_value: 0.01,
          },
        ],
      },
    },
  },
  enable_sentry: {
    sync: true,
    description: 'If true, enable Sentry error reporting with client version information. For legacy reasons, intellij_enable_sentry and beachhead_enable_sentry control configurations for those clients.',
    envs: {
      production: {
        rules: [
          {
            client: 'vscode',
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            // Only enable Sentry for dogfood right now
            return_value: false,
          },
        ],
      },
    },
  },
  webview_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for webview errors.',
    envs: {
      production: {
        rules: [
          {
            client: 'vscode',
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.1,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  webview_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for webview traces.',
    envs: {
      production: {
        rules: [
          {
            client: 'vscode',
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.1,
          },
          {
            return_value: 0.00,
          },
        ],
      },
    },
  },

  intellij_edt_freeze_detection_enabled: {
    sync: true,
    description: 'Enable logging, collection of thread dumps, and reporting of EDT freezes in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          // Keeping explicit tenant configuration for pollyex and silanano for future use.
          // Feature temporarily disabled for these external user tenants due to performance issues.
          {
            tenant_name: [
              'pollyex',
              'silanano',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_agent_auto_mode: {
    sync: true,
    description: 'If true, the agent auto mode is enabled.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_remote_agent_ssh_min_version: {
    sync: true,
    description: 'When SSHing to a remote agent, the minimum version of the Augment extension that will be installed on the remote machine.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.456.0',
          },
        ],
      },
    },
  },
  enable_completion_session_state: {
    sync: true,
    description: 'If true, completion host will cancel requests via session state.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_completion_retrieval_cancel: {
    sync: true,
    description: 'If true, completion host will allow retrieval to cancel completion requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_completion_memory_monitoring: {
    sync: true,
    description: 'If true, completion host will monitor memory usage.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  grep_search_tool_enable: {
    sync: true,
    description: 'Enable the grep search tool for agents.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  grep_search_tool_timelimit_sec: {
    sync: true,
    description: 'Time limit for grep search tool execution in seconds.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  chat_server_enable_error_details_metadata: {
    sync: true,
    description: 'Enable error details metadata in chat server gRPC responses.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  grep_search_tool_output_chars_limit: {
    sync: true,
    description: 'Character limit for grep search tool output.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  grep_search_tool_num_context_lines: {
    sync: true,
    description: 'Number of context lines to include before and after each match in grep search tool output.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  agent_report_streamed_chat_every_chunk: {
    sync: true,
    description: 'How often to report the chat history when streaming (number of chunks)',
    envs: {
      production: {
        rules: [
          {
            return_value: 3,
          },
        ],
      },
    },
  },
  agent_max_total_changed_files_size_bytes: {
    sync: true,
    description: 'Max total size of changed files to send in a single chat update (bytes)',
    envs: {
      production: {
        rules: [
          {
            return_value: 2 * 1024 * 1024,
          },
        ],
      },
    },
  },
  agent_max_changed_files_skipped_paths: {
    sync: true,
    description: 'Max number of skipped paths to send when the total changed files size is exceeded',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  agent_idle_status_update_interval_ms: {
    sync: true,
    description: 'How often to update the agent status when idle (milliseconds)',
    envs: {
      production: {
        rules: [
          {
            return_value: 60 * 1000,
          },
        ],
      },
    },
  },
  agent_max_iterations: {
    sync: true,
    description: 'Maximum number of iterations in a single turn for the agent loop',
    envs: {
      production: {
        rules: [
          {
            return_value: 250,
          },
        ],
      },
    },
  },
  agent_ssh_connection_check_interval_ms: {
    sync: true,
    description: 'Agent SSH connection check interval (milliseconds)',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  agent_ssh_connection_check_log_interval_ms: {
    sync: true,
    description: 'Agent SSH connection check log interval (milliseconds)',
    envs: {
      production: {
        rules: [
          {
            return_value: 5 * 60 * 1000,
          },
        ],
      },
    },
  },
  intellij_indexing_v3_enabled: {
    sync: true,
    description: 'If true, the intellij client will use indexing v3.',
    envs: {
      production: {
        rules: [
          // Users opting in to v3
          {
            user_uuid: [
              'b0cd763d-4fea-4dc1-8907-4d37b79fd510',
              '91d1716d-787a-415d-a933-7cd97232d3c9',
              '2ace7849-aa75-49b0-9568-16a1809b27af',
              '7461f574-ca49-40d1-91b7-92b4d7c623ee',
              'b82f3f83-328a-486f-88a9-b4a4b1b6eab2',
              'b054b923-d72f-4ff6-bcb0-59fa135d1f66',
              'f00683f3-bbf0-4941-af2a-4b1594547d1e',
              'f3c7f2cf-f7c1-4bdf-9c63-8b959fca728f',
              '031e7744-934e-4e9d-a94e-de59e5a15358',
              '5310f1fd-d6c7-4508-bccb-80c4bf915ac2',
              '0549ed09-87b6-4307-a248-dec5b96c6173',
              'cb8663fd-cce0-457c-88f7-a4ae0f3c6cfa',
              '5c3a3bb5-a18a-46fa-bebe-545e7c941036',
              '5a442af4-ab6e-48ba-9112-9361c12092c1',
              'dc44df98-a689-4bd6-861f-a4c2f314c69c',
              '1307d4e2-5264-467b-a61d-5ad6c4edb974',
              '236ec1d8-9cc5-4826-9321-e4c4b836001e',
              '5eb37183-ed8d-4f4c-b17e-5351c76a08bc',
              '02bf8c1b-0a2c-4712-99b8-8d2964b15cf2',
            ],
            client: 'intellij',
            min_client_version: '0.249.1',
            return_value: true,
          },
          // Eanbled for tenants
          {
            tenant_name: [
              'i0-vanguard0',
              'adobe',
            ],
            client: 'intellij',
            min_client_version: '0.249.1',
            return_value: true,
          },
          {
            // Wave 1
            tenant_name: [
              'webpros',
              'striim',
              'ringcentral',
              'intercom',
              'gofundme',
              'lendable',
              'tekion-us',
              'aitutor-mercor',
              'aitutor-turing',
            ],
            client: 'intellij',
            min_client_version: '0.249.2',
            return_value: true,
          },
          {
            // Everyone
            client: 'intellij',
            min_client_version: '0.249.2',
            return_value: true,
          },
          // Enabled for staging on all versions
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          // Disable for everyone else
          {
            return_value: false,
          },
        ],
      },
    },
  },
  history_summary_min_version: {
    sync: true,
    description: 'Sets the minimum version number to enable history summarization in chat conversations. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  history_summary_max_chars: {
    sync: true,
    description: 'Maximum expected characters before history summarization is triggered. Deprecated. Use history_summary_params instead.',
    envs: {
      production: {
        rules: [
          {
            return_value: 200000,
          },
        ],
      },
    },
  },
  history_summary_lower_chars: {
    sync: true,
    description: 'Lower threshold for history summarization. Deprecated. Use history_summary_params instead.',
    envs: {
      production: {
        rules: [
          {
            return_value: 80000,
          },
        ],
      },
    },
  },
  history_summary_params: {
    sync: true,
    description: 'All parameters for chat history summarization functionality',
    envs: {
      production: {
        rules: [
          {
            return_value: {
              trigger_on_history_size_chars: 200000,
              history_tail_size_chars_to_exclude: 80000,
              trigger_on_history_size_chars_when_cache_expiring: 140000,
              cache_ttl_ms: 5 * 60 * 1000,  // 5 min
              buffer_time_before_cache_expiration_ms: 30 * 1000,  // 30 sec
              prompt: |||
                Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
                This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.

                Your summary should be structured as follows:
                Context: The context to continue the conversation with. If applicable based on the current task, this should include:
                1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.
                2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.
                3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.
                4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.
                5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.
                6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.

                Example summary structure:
                1. Previous Conversation:
                [Detailed description]
                2. Current Work:
                [Detailed description]
                3. Key Technical Concepts:
                - [Concept 1]
                - [Concept 2]
                - [...]
                4. Relevant Files and Code:
                - [File Name 1]
                    - [Summary of why this file is important]
                    - [Summary of the changes made to this file, if any]
                    - [Important Code Snippet]
                - [File Name 2]
                    - [Important Code Snippet]
                - [...]
                5. Problem Solving:
                [Detailed description]
                6. Pending Tasks and Next Steps:
                - [Task 1 details & next steps]
                - [Task 2 details & next steps]
                - [...]

                Output only the summary of the conversation so far, without any additional commentary or explanation.
              |||,
            },
          },
        ],
      },
    },
  },
  enable_viewed_content_tracking: {
    sync: true,
    description: 'If true, enable viewed content tracking in VSCode client to maintain history of viewed files and their visible content.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
              'i0',
              'i1',
            ],
            client: 'vscode',
            min_client_version: '0.522.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  viewed_content_close_range_threshold: {
    sync: true,
    description: 'Threshold for considering positions "close" in viewed content tracking (lines).',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  viewed_content_discrete_jump_threshold: {
    sync: true,
    description: 'Threshold for considering a change a "discrete jump" in viewed content tracking (lines).',
    envs: {
      production: {
        rules: [
          {
            return_value: 15,
          },
        ],
      },
    },
  },
  viewed_content_min_event_age_ms: {
    sync: true,
    description: 'Minimum age before accepting a pending event in viewed content tracking (milliseconds).',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  viewed_content_max_event_age_ms: {
    sync: true,
    description: 'Maximum age of a pending event before we reject it regardless of other criteria in viewed content tracking (milliseconds).',
    envs: {
      production: {
        rules: [
          {
            return_value: 30000,
          },
        ],
      },
    },
  },
  auth_central_windsurf_promotion_enabled: {
    sync: true,
    description: 'If true, auth central endpoints to get and process the Windsurf promotion will be enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_cursor_promotion_enabled: {
    sync: true,
    description: 'If true, auth central endpoints to get and process the Cursor promotion will be enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  history_summary_prompt: {
    sync: true,
    description: 'Custom prompt for conversation history summarizatio. Deprecated. Use history_summary_params instead.',
    envs: {
      production: {
        rules: [
          {
            return_value: |||
              Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
              This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.

              Your summary should be structured as follows:
              Context: The context to continue the conversation with. If applicable based on the current task, this should include:
              1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.
              2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.
              3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.
              4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.
              5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.
              6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.

              Example summary structure:
              1. Previous Conversation:
              [Detailed description]
              2. Current Work:
              [Detailed description]
              3. Key Technical Concepts:
              - [Concept 1]
              - [Concept 2]
              - [...]
              4. Relevant Files and Code:
              - [File Name 1]
                  - [Summary of why this file is important]
                  - [Summary of the changes made to this file, if any]
                  - [Important Code Snippet]
              - [File Name 2]
                  - [Important Code Snippet]
              - [...]
              5. Problem Solving:
              [Detailed description]
              6. Pending Tasks and Next Steps:
              - [Task 1 details & next steps]
              - [Task 2 details & next steps]
              - [...]

              Output only the summary of the conversation so far, without any additional commentary or explanation.
            |||,
          },
        ],
      },
    },
  },
  customer_ui_windsurf_promotion_enabled: {
    sync: true,
    description: 'If true, the Windsurf promotion page will be enabled on the customer UI. If false, the page will not be accessible.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  customer_ui_cursor_promotion_enabled: {
    sync: true,
    description: 'If true, the Cursor promotion page will be enabled on the customer UI. If false, the page will not be accessible.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  customer_ui_content_deletion_enabled: {
    sync: true,
    description: 'If true, customer UI shows users a button to delete their indexed code',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_promotion_recaptcha_threshold: {
    sync: true,
    description: 'reCAPTCHA threshold for promotions. If the score is below this threshold, the promotion will not be granted.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.5,
          },
        ],
      },
    },
  },
  enable_commit_indexing: {
    sync: true,
    description: 'If true, enables git commit indexing functionality in the client',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'vscode',
            min_client_version: '0.488.0',
            return_value: true,
          },
          {
            client: 'vscode',
            min_client_version: '0.498.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_commits_to_index: {
    sync: true,
    description: 'Maximum number of commits to index for git commit indexing',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 10000,
          },
          {
            namespace: self_serve_namespaces,
            client: 'vscode',
            min_client_version: '0.498.0',
            return_value: 2000,
          },
          {
            client: 'vscode',
            min_client_version: '0.498.0',
            return_value: 3000,
          },
          // When enabling for intellij, ease into it.
          // Low number of commits, increase day by day as
          // gemini bandwidth permits.
          // We can relax this if (a) we're more resilient to
          // gemini rate limit and (b) we're OK with backlogs
          // taking a while to drain.
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  client_deprecated: {
    sync: true,
    description: 'If true, the client version is deprecated and the user should be shown a message urging upgrade',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'vscode',
            max_client_version: '0.500.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'intellij',
            max_client_version: '0.200.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_exchange_storage: {
    sync: true,
    description: 'If true, enables exchange storage functionality for dehydrating conversations by storing exchanges in sidecar storage',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: ['intellij'],
            min_client_version: '0.250.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: ['vscode'],
            min_client_version: '0.512.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_tool_use_state_storage: {
    sync: true,
    description: 'If true, enables tool use state storage functionality for persisting tool execution states in sidecar storage',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: ['intellij'],
            min_client_version: '0.250.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: ['vscode'],
            min_client_version: '0.512.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  customer_ui_enable_user_feature_stats: {
    sync: true,
    description: 'If true, the user feature stats are enabled in customer UI.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  customer_ui_enable_promotion_content_validation: {
    sync: true,
    description: 'If true, the promotion content validation is enabled in customer UI. This is disabled for now to keep users as happy as possible, but if we see too many signups / fraud, we should enable thiss.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  billing_event_processor_require_user: {
    sync: true,
    description: 'If true, the billing event processor will require a user to be present in bigtable before processing the event. This should only be true in staging and dev, and false in production. This is because, since all dev instances share one Orb instance, we only want it to apply to the dev instance that created the user.',
    envs: {
      production: {
        rules: [
          {
            env: ['STAGING'],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_react_frontend: {
    sync: true,
    description: 'If true, the auth central react frontend is enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  settings_bypass_version_check: {
    sync: true,
    description: 'If true, the settings service will bypass the check and mutate operation, so it will always update settings regarldess of the version number provided. This is used for tenants who have CMK enabled until we fix AU-11331.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'callminer',
              'rubrik-cmk',
              'intuit',
              'maxar-cmk',
              'striim',
              'ysecurity-cmk',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  remote_agents_generate_summary_model_name: {
    sync: true,
    description: 'The model name to use for remote agents generate summary requests. This allows us to configure which model is used for generating summaries for remote agents diffs',
    envs: {
      production: {
        rules: [
          {
            return_value: 'gemini-2-5-flash-v9-c4-p2-chat',
          },
        ],
      },
    },
  },
  remote_agent_actions_llm_title_model_name: {
    sync: true,
    description: 'The model name to use for generating user-friendly titles for remote agents created from triggers.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'gemini-2-5-flash-v9-c4-p2-chat',
          },
        ],
      },
    },
  },
  remote_agent_actions_enable_endpoints: {
    sync: true,
    description: 'If true, remote agent actions API endpoints are enabled. Used to roll out remote agent actions incrementally while it is under development.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  remote_agent_actions_enable_webhook_trigger: {
    sync: true,
    description: 'If true, webhook-triggered remote agent actions are enabled. Used to roll out webhook triggers incrementally while it is under development.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  retry_chat_stream_timeouts: {
    sync: true,
    description: 'Enables retry behavior for webview message timeouts in chat stream AsyncMsgSender calls',
    envs: {
      production: {
        rules: [
          {
            client: ['vscode', 'intellij'],
            min_client_version: '0.0.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  remote_agent_current_workspace: {
    sync: true,
    description: 'If true, enables current workspace option for remote agent creation',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: ['vscode'],
            min_client_version: '0.0.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  block_trial_to_community_conversions: {
    sync: true,
    description: 'If true, blocks free trial users from converting to community plans',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  block_paid_to_community_conversions: {
    sync: true,
    description: 'If true, blocks paid plan users from converting to community plans',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_cli_access_enabled: {
    sync: true,
    description: 'If true, CLI access is enabled for the tenant. When false, CLI requests will be blocked with a forbidden error.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            tenant_name: cli_enterprise_tenant_allowlist,
            return_value: true,
          },
          {
            tenant_name: cli_enterprise_tenant_trials,
            return_value: true,
          },
          {
            user_uuid: cli_self_serve_users,
            return_value: true,
          },
          {
            namespace: self_serve_namespaces,
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_tool_turns_per_user_message: {
    sync: true,
    description: 'The maximum number of consecutive tool result turns allowed in a chat conversation before asking the user if they want to continue.',
    envs: {
      production: {
        rules: [
          {
            return_value: 100,
          },
        ],
      },
    },
  },
  enable_memory_retrieval: {
    sync: true,
    description: 'If true, enables memory retrieval functionality.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'intellij',
            min_client_version: '0.267.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
              'aitutor-mercor',
              'aitutor-turing',
            ],
            client: 'vscode',
            min_client_version: '0.515.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_agent_tabs: {
    sync: true,
    description: 'If true, enables agent tabs functionality in VSCode and IntelliJ clients.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'vscode',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_swarm_mode: {
    sync: true,
    description: 'If true, enables swarm mode functionality in VSCode extension for multi-agent workflows',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            min_client_version: '0.501.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_agent_git_tracker: {
    sync: true,
    description: 'If true, enables agent git tracker functionality across clients.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'vscode',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  notification_max_notifications_per_request: {
    sync: true,
    description: 'The maximum number of notifications that can be returned in a single request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  enable_grouped_tools: {
    sync: true,
    description: 'If true, enables grouped tools functionality in the chat interface for better tool organization and display',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_parallel_tools: {
    sync: true,
    description: 'If true, enables parallel tools',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  beachhead_enable_sub_agent_tool: {
    sync: true,
    description: 'If true, enables sub-agent tool functionality in beachhead for spawning local isolated sub-agents',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  third_party_arbiter_session_mapping_disable_fallback: {
    sync: true,
    description: 'If true, disable fallback targets if a session has a target mapping. Enable if: all targets rate limiting and cache ratio suffering',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  third_party_arbiter_weight_override: {
    sync: true,
    description: 'JSON blob to override load balance weights for third party arbiter service.',
    // To be used for manual adjustment during incidents until automatic rebalancing with
    // more stable session mapping is finished.
    // Format (see deploy.jsonnet): {"model_name": {"client_name": weight, ...}, ...}
    envs: {
      production: {
        rules: [
          {
            return_value: {
              // Currently deployed: 44, 6, 20, 30
              claude_4_0_sonnet: {
                anthropic_direct: 50.0,
                anthropic_vertexai_us_e5: 4.0,  // Leaving higher than I want so that we might use Provisioned
                anthropic_vertexai_eu_w1: 12.0,
                anthropic_vertexai_as_se1: 34.0,
              },
            },
          },
        ],
      },
    },
  },
  third_party_arbiter_fallback_target_count: {
    sync: true,
    description: 'Number of fallback targets to provide from GetTarget. Controls session mobility between targets.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1,
          },
        ],
      },
    },
  },
  enable_native_remote_mcp: {
    sync: true,
    description: 'If true, enables the remote mcp OAuth native integration for Stripe and Sentry',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  bigtable_proxy_log_latency_threshold_s: {
    sync: true,
    description: 'Latency threshold in seconds for logging bigtable proxy operations',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.1,
          },
        ],
      },
    },
  },
  tenant_watcher_write_spanner: {
    sync: true,
    description: 'If true, tenant watcher will double-write to spanner',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  truncation_footer_addition_text: {
    sync: true,
    description: 'Additional text to add to the truncation footer to reduce untruncated content usage',
    envs: {
      production: {
        rules: [
          {
            return_value: |||
              **Only use view-range-untruncated or search-untruncated tools if additional output is strictly necessary to continue**, such as when:
              - You need to find specific error details that are clearly truncated
              - You need to search for specific patterns or text that might be elsewhere in the output
              - The truncated output is genuinely insufficient for the task at hand

              If you do need to use these tools:
              - For view-range-untruncated: Request only the specific line ranges you actually need
              - For search-untruncated: Use specific search terms rather than viewing large ranges
            |||,
          },
        ],
      },
    },
  },
  agent_view_tool_params: {
    sync: true,
    description: 'Configuration parameters for agent view tool behavior, including depth and directory limits per depth',
    envs: {
      production: {
        rules: [
          {
            // show all for depth 1, show max of 50 for depth 2
            return_value: '{ "max_depth": 2, "view_dir_max_entries_per_depth": [-1, 50]}',
          },
        ],
      },
    },
  },
  vscode_terminal_strategy: {
    sync: true,
    description: 'Terminal completion detection strategy for VSCode (vscode_events or script_capture)',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'script_capture',
          },
          {
            return_value: 'vscode_events',
          },
        ],
      },
    },
  },
  content_manager_gc_enabled: {
    sync: true,
    description: 'Circuit breaker for Content Manager garbage collection. Set to false to halt all GC operations.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  checkpoint_indexer_regex_filter: {
    sync: true,
    description: 'Regex filter to apply to checkpoint paths before indexing',
    envs: {
      production: {
        rules: [
          {
            return_value: 'a^',  // matches nothing
          },
        ],
      },
    },
  },
  workingset_enable_checkpoint_renewal: {
    sync: true,
    description: 'Enable checkpoint renewal logic in working set service',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  stale_models: {
    sync: true,
    description: 'List of stale models to respond with a fixed message asking if the user would like to continue',
    envs: {
      production: {
        rules: [
          {
            return_value: '{"model_list": []}',
          },
        ],
      },
    },
  },
  stale_models_response: {
    sync: true,
    description: 'Response to send when a stale model is requested',
    envs: {
      production: {
        rules: [
          {
            return_value: '"The model that you are using is no longer available. Please try again with a different model, or restart your extension."',
          },
        ],
      },
    },
  },
  gate_model_picker_for_community_and_trials: {
    sync: true,
    description: 'If true, the model picker will be gated for community and legacy trial users',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
}
